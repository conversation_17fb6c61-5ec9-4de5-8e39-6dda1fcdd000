[build]
  command = "npm run build"
  publish = "dist"
  base = "."

# Redirects for SPA routing
[[redirects]]
  from = "/api/*"
  to = "https://safety-api.rl5j77.easypanel.host/api/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Cache-Control = "public, max-age=********, immutable"

# Cache control for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Cache control for API responses
[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
