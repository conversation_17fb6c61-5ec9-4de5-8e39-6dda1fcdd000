import { User, RolePermissions } from '../types/auth.types';

/**
 * Role-based permissions configuration
 */
const ROLE_PERMISSIONS: RolePermissions = {
  admin: [
    'profile:read',
    'profile:update',
    'dashboard:read',
    'users:read',
    'users:update',
    'users:create',
    'visitors:read',
    'visitors:update',
    'visitors:create',
    'visitors:approve',
    'visitors:reject',
    'content:read',
    'content:update',
    'content:create',
    'organizations:read',
    'backup:read',
    'logs:read',
  ],
  user: [
    'profile:read',
    'profile:update',
    'dashboard:read',
    'visitors:read',
  ],
  superadmin: [], // superadmin has access to everything, so we don't need to list permissions
};

/**
 * Check if user has permission for specific resource and action
 * @param user - Current user object
 * @param resource - Resource name (e.g., 'users', 'visitors')
 * @param action - Action name (e.g., 'read', 'create', 'update')
 * @returns boolean indicating if user has permission
 */
export function checkPermission(
  user: User | null,
  resource: string,
  action: string
): boolean {
  if (!user) {
    console.log('❌ No user provided for permission check');
    return false;
  }

  // superadmin has access to everything
  if (user.role === 'superadmin') {
    console.log('✅ superadmin access granted for:', `${resource}:${action}`);
    return true;
  }

  // Check role-based permissions
  const userPermissions = ROLE_PERMISSIONS[user.role] || [];
  const hasPermission = userPermissions.includes(`${resource}:${action}`);
  
  console.log(`🔐 Permission check: ${user.role} ${resource}:${action} = ${hasPermission}`);
  return hasPermission;
}

/**
 * Get all permissions for a specific role
 * @param role - User role
 * @returns Array of permission strings
 */
export function getRolePermissions(role: User['role']): string[] {
  if (role === 'superadmin') {
    // Return all possible permissions for superadmin
    return Object.values(ROLE_PERMISSIONS).flat();
  }
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check if user has any of the specified permissions
 * @param user - Current user object
 * @param permissions - Array of permission strings to check
 * @returns boolean indicating if user has at least one permission
 */
export function hasAnyPermission(
  user: User | null,
  permissions: string[]
): boolean {
  if (!user) return false;
  
  if (user.role === 'superadmin') return true;
  
  const userPermissions = ROLE_PERMISSIONS[user.role] || [];
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * Check if user has all of the specified permissions
 * @param user - Current user object
 * @param permissions - Array of permission strings to check
 * @returns boolean indicating if user has all permissions
 */
export function hasAllPermissions(
  user: User | null,
  permissions: string[]
): boolean {
  if (!user) return false;
  
  if (user.role === 'superadmin') return true;
  
  const userPermissions = ROLE_PERMISSIONS[user.role] || [];
  return permissions.every(permission => userPermissions.includes(permission));
}