import { Navigate } from 'react-router-dom';
import { useSession } from '../../lib/auth-client';
import { VisitorSafetyQuiz } from '../../pages/user/VisitorSafetyQuiz';

/**
 * Component to handle visitor quiz route with proper role-based redirects
 * Ensures that authenticated superadmin and admin users are redirected to their dashboards
 * while allowing unauthenticated users and regular users to access the quiz
 */
export function VisitorQuizRoute() {
  const { data: session } = useSession();
  
  // If user is authenticated, redirect based on role
  if (session?.user) {
    const userRole = session.user.role;
    
    if (userRole === 'superadmin') {
      return <Navigate to="/superadmin/dashboard" replace />;
    }
    
    if (userRole === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    }
  }
  
  // Allow unauthenticated users and regular users to access the quiz
  return <VisitorSafetyQuiz />;
}