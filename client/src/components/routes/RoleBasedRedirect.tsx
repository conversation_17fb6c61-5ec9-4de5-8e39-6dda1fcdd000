import { Navigate } from 'react-router-dom';
import { useSession } from '../../lib/auth-client';

/**
 * Component to handle role-based redirects for authenticated users
 * Redirects users to their appropriate dashboard based on their role
 */
export function RoleBasedRedirect() {
  const { data: session, isPending } = useSession();

  // Show loading while checking session
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If user is authenticated, redirect based on role
  if (session?.user) {
    const userRole = session.user.role;
    
    if (userRole === 'superadmin') {
      return <Navigate to="/superadmin/dashboard" replace />;
    }
    
    if (userRole === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    }
    
    if (userRole === 'user') {
      return <Navigate to="/user/quiz" replace />;
    }
    
    // Fallback for unknown roles
    return <Navigate to="/login" replace />;
  }

  // If not authenticated, redirect to login
  return <Navigate to="/login" replace />;
}