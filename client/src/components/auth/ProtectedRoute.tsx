import { ReactNode, useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useSession } from '../../lib/auth-client';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: 'user' | 'admin' | 'superadmin';
  allowedRoles?: ('user' | 'admin' | 'superadmin')[];
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  allowedRoles 
}: ProtectedRouteProps) {
  const { data: session, isPending } = useSession();
  const [isInitializing, setIsInitializing] = useState(true);

  // Prevent flash by adding a small delay for initial load
  useEffect(() => {
    if (!isPending) {
      const timer = setTimeout(() => {
        setIsInitializing(false);
      }, 100); // Small delay to prevent flash
      return () => clearTimeout(timer);
    }
  }, [isPending]);

  // Show loading while session is being fetched or during initialization
  if (isPending || isInitializing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect to appropriate login page based on current path
  if (!session?.user) {
    const isUserPath = window.location.pathname.startsWith('/user');
    return <Navigate to={isUserPath ? "/user/login" : "/login"} replace />;
  }

  const userRole = session.user.role as 'user' | 'admin' | 'superadmin';

  // Check role requirement first (RBAC priority)
  if (requiredRole) {
    const roleHierarchy: Record<string, number> = { user: 1, admin: 2, superadmin: 3 };
    const userLevel = roleHierarchy[userRole] ?? 0;
    const requiredLevel = roleHierarchy[requiredRole] ?? 0;

    if (userLevel < requiredLevel) {
      // Redirect instead of showing Access Denied to prevent flash
      const isUserPath = window.location.pathname.startsWith('/user');
      return <Navigate to={isUserPath ? "/user/login" : "/login"} replace />;
    }
  }

  // Check allowed roles
  if (allowedRoles && !allowedRoles.includes(userRole)) {
    // Redirect instead of showing Access Denied to prevent flash
    const isUserPath = window.location.pathname.startsWith('/user');
    return <Navigate to={isUserPath ? "/user/login" : "/login"} replace />;
  }

  return <>{children}</>;
}
