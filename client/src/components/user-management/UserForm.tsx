import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import { UserFormData } from '../../hooks/user-management/useUserForm';
import { Organization } from '../../types/auth.types';
import { CustomRoleWithPermissions } from '../../services/auth/role-management.service';

interface UserFormProps {
  formData: UserFormData;
  formErrors: Record<string, string>;
  organizations: Organization[];
  customRoles: CustomRoleWithPermissions[];
  isEdit?: boolean;
  onFormDataChange: (data: UserFormData) => void;
}

export function UserForm({
  formData,
  formErrors,
  organizations,
  customRoles,
  isEdit = false,
  onFormDataChange
}: UserFormProps) {
  const handleInputChange = (field: keyof UserFormData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Nama *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          placeholder="Masukkan nama pengguna"
        />
        {formErrors.name && (
          <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Email *
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          placeholder="Masukkan email pengguna"
        />
        {formErrors.email && (
          <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Password {!isEdit && '*'}
        </label>
        <input
          type="password"
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          placeholder={isEdit ? "Kosongkan jika tidak ingin mengubah password" : "Masukkan password"}
        />
        {formErrors.password && (
          <p className="text-red-500 text-sm mt-1">{formErrors.password}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Role *
        </label>
        <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="user">User</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="superadmin">Super Admin</SelectItem>
            <SelectItem value="custom">Custom Role</SelectItem>
          </SelectContent>
        </Select>
        {formErrors.role && (
          <p className="text-red-500 text-sm mt-1">{formErrors.role}</p>
        )}
      </div>

      {formData.role === 'custom' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Custom Role *
          </label>
          <Select 
            value={formData.customRoleId || ''} 
            onValueChange={(value) => handleInputChange('customRoleId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Pilih custom role" />
            </SelectTrigger>
            <SelectContent>
              {customRoles.map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formErrors.customRoleId && (
            <p className="text-sm text-red-600">{formErrors.customRoleId}</p>
          )}
        </div>
      )}

      {formData.role !== 'superadmin' && formData.role !== 'custom' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Organisasi/Perusahaan
          </label>
          <Select 
            value={formData.organizationId} 
            onValueChange={(value) => handleInputChange('organizationId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Pilih organisasi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Tidak ada organisasi</SelectItem>
              {organizations.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {formErrors.organizationId && (
            <p className="text-red-500 text-sm mt-1">{formErrors.organizationId}</p>
          )}
        </div>
      )}
    </div>
  );
}