import { Button } from '../../ui/button';
import { X, Loader2, AlertTriangle } from 'lucide-react';
import { UserWithOrganization } from '../../../hooks/user-management/useUsers';

interface DeleteUserModalProps {
  isOpen: boolean;
  user: UserWithOrganization | null;
  submitting: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export function DeleteUserModal({
  isOpen,
  user,
  submitting,
  onClose,
  onConfirm
}: DeleteUserModalProps) {
  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-red-600">Hapus Pengguna</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            disabled={submitting}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-start gap-3 mb-6">
          <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-gray-900 mb-2">
              Apakah Anda yakin ingin menghapus pengguna <strong>{user.name}</strong>?
            </p>
            <p className="text-sm text-gray-600">
              Tindakan ini tidak dapat dibatalkan. Semua data yang terkait dengan pengguna ini akan dihapus secara permanen.
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={submitting}
          >
            Batal
          </Button>
          <Button
            onClick={onConfirm}
            disabled={submitting}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Menghapus...
              </>
            ) : (
              'Hapus'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}