import { Button } from '../../ui/button';
import { X, Loader2 } from 'lucide-react';
import { UserForm } from '../UserForm';
import { UserFormData } from '../../../hooks/user-management/useUserForm';
import { Organization } from '../../../types/auth.types';
import { CustomRoleWithPermissions } from '../../../services/auth/role-management.service';

interface EditUserModalProps {
  isOpen: boolean;
  formData: UserFormData;
  formErrors: Record<string, string>;
  organizations: Organization[];
  customRoles: CustomRoleWithPermissions[];
  submitting: boolean;
  onClose: () => void;
  onFormDataChange: (data: UserFormData) => void;
  onSubmit: () => void;
}

export function EditUserModal({
  isOpen,
  formData,
  formErrors,
  organizations,
  customRoles,
  submitting,
  onClose,
  onFormDataChange,
  onSubmit
}: EditUserModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Edit Pengguna</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            disabled={submitting}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {formErrors.general && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{formErrors.general}</p>
          </div>
        )}

        <UserForm
          formData={formData}
          formErrors={formErrors}
          organizations={organizations}
          customRoles={customRoles}
          isEdit={true}
          onFormDataChange={onFormDataChange}
        />

        <div className="flex justify-end gap-3 mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={submitting}
          >
            Batal
          </Button>
          <Button
            onClick={onSubmit}
            disabled={submitting}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Memperbarui...
              </>
            ) : (
              'Perbarui'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}