import { Button } from '../ui/button';

interface PaginationProps {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  loading: boolean;
  onPrevPage: () => void;
  onNextPage: () => void;
}

export function Pagination({
  currentPage,
  pageSize,
  totalItems,
  loading,
  onPrevPage,
  onNextPage
}: PaginationProps) {
  if (loading || totalItems === 0) return null;

  return (
    <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
      <div>
        Menampilkan {totalItems} pengguna
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onPrevPage}
          disabled={currentPage === 0}
        >
          Sebelumnya
        </Button>
        <span>Halaman {currentPage + 1}</span>
        <Button
          variant="outline"
          size="sm"
          onClick={onNextPage}
          disabled={totalItems < pageSize}
        >
          Selanjutnya
        </Button>
      </div>
    </div>
  );
}