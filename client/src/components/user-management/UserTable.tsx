import { But<PERSON> } from '../ui/button';
import { Edit, Trash2, Loader2 } from 'lucide-react';
import { UserWithOrganization } from '../../hooks/user-management/useUsers';
import { Organization } from '../../types/auth.types';

interface UserTableProps {
  users: UserWithOrganization[];
  loading: boolean;
  searchQuery: string;
  onEditUser: (user: UserWithOrganization) => void;
  onDeleteUser: (user: UserWithOrganization) => void;
}

export function UserTable({
  users,
  loading,
  searchQuery,
  onEditUser,
  onDeleteUser
}: UserTableProps) {
  const getOrganizationNames = (organizations: Organization[]) => {
    if (!organizations || organizations.length === 0) {
      return '-';
    }
    return organizations.map(org => org.name).join(', ');
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'superadmin':
        return 'bg-red-100 text-red-800';
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      case 'user':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b bg-gray-50">
            <th className="text-left p-4 font-medium text-gray-700">Nama</th>
            <th className="text-left p-4 font-medium text-gray-700">Email</th>
            <th className="text-left p-4 font-medium text-gray-700">Role</th>
            <th className="text-left p-4 font-medium text-gray-700">Organisasi/Perusahaan</th>
            <th className="text-left p-4 font-medium text-gray-700">Aksi</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr>
              <td colSpan={5} className="p-8 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-gray-500">Memuat data pengguna...</span>
                </div>
              </td>
            </tr>
          ) : users.length === 0 ? (
            <tr>
              <td colSpan={5} className="p-8 text-center text-gray-500">
                {searchQuery ? 'Tidak ada pengguna yang ditemukan' : 'Belum ada data pengguna'}
              </td>
            </tr>
          ) : (
            users.map((user) => (
              <tr key={user.id} className="border-b hover:bg-gray-50">
                <td className="p-4">
                  <div className="font-medium text-gray-900">{user.name}</div>
                </td>
                <td className="p-4">
                  <div className="text-gray-600">{user.email}</div>
                </td>
                <td className="p-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(user.role)}`}>
                    {user.role === 'superadmin' ? 'Super Admin' : user.role === 'admin' ? 'Admin' : 'User'}
                  </span>
                </td>
                <td className="p-4">
                  <div className="text-gray-600">
                    {user.role === 'superadmin' ? 'Tidak ada organisasi' : getOrganizationNames(user.organizations)}
                  </div>
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditUser(user)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDeleteUser(user)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}