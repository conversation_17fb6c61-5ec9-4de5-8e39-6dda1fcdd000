import { Button } from '../ui/button';
import { PAMLogo } from '../ui/PAMLogo';
import { LogOut, Play } from 'lucide-react';

interface Video {
  id: string;
  title: string;
  youtubeUrl: string;
}

interface VideoStepProps {
  video?: Video | null;
  loading?: boolean;
  error?: string | null;
  onVideoComplete: () => void;
  onLogout: () => void;
}

export function VideoStep({ video, loading, error, onVideoComplete, onLogout }: VideoStepProps) {
  // Function to extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
  };
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 pt-8 pb-6 px-6">
          <div className="flex justify-between items-start">
            <PAMLogo width={90} height={90} />
            <Button
              onClick={onLogout}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-6 pb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">Visitor Safety Quiz</h1>

          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading video...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button
                onClick={onVideoComplete}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                Lanjut ke Quiz
              </Button>
            </div>
          )}

          {!loading && !error && video && (
            <>
              {/* Video Player */}
              <div className="relative bg-gray-900 rounded-lg overflow-hidden mb-8">
                {video.youtubeUrl ? (
                  <div className="aspect-video">
                    <iframe
                      src={`https://www.youtube.com/embed/${getYouTubeVideoId(video.youtubeUrl)}?rel=0&modestbranding=1`}
                      title={video.title}
                      className="w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                ) : (
                  <div className="aspect-video bg-gradient-to-br from-orange-400 to-red-600 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4 mx-auto">
                        <Play className="h-8 w-8 ml-1" />
                      </div>
                      <p className="text-sm opacity-90">{video.title}</p>
                    </div>
                  </div>
                )}
              </div>

              <Button
                onClick={onVideoComplete}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                Lanjut ke Quiz
              </Button>
            </>
          )}

          {!loading && !error && !video && (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">Tidak ada video yang tersedia</p>
              <Button
                onClick={onVideoComplete}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                Lanjut ke Quiz
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}