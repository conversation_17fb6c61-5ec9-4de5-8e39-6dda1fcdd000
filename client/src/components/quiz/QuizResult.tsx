import { But<PERSON> } from '../ui/button';
import { PAMLogo } from '../ui/PAMLogo';
import { LogOut } from 'lucide-react';
import { QuizResultProps } from '../../types/quiz.types';
import { DigitalPermit } from './DigitalPermit';
import { FeedbackSection } from './FeedbackSection';
import { SharePermitSection } from './SharePermitSection';

export function QuizResult({ score, onRetake, onLogout }: QuizResultProps) {
  const isPassed = score >= 80;

  if (!isPassed) {
    // Failed Result
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0 pt-8 pb-6 px-6">
            <div className="flex justify-between items-start">
              <PAMLogo width={90} height={90} />
              <Button
                onClick={onLogout}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2 text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 px-6 pb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-8">Visitor Safety Quiz</h1>

            <div className="text-center mb-8">
              <p className="text-lg text-gray-700 mb-2">Perolehan Skor:</p>
              <p className="text-4xl font-bold text-red-600 mb-8">{score}%</p>
            </div>

            <Button
              onClick={onRetake}
              className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
            >
              Ulang Quiz
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Passed Result with Digital Permit
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 pt-8 pb-6 px-6">
          <div className="flex justify-between items-start">
            <PAMLogo width={90} height={90} />
            <Button
              onClick={onLogout}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-6 pb-6 space-y-6">
          <h1 className="text-2xl font-bold text-gray-900">Visitor Safety Quiz</h1>

          <div>
            <p className="text-lg text-gray-700 mb-2">Perolehan Skor:</p>
            <p className="text-4xl font-bold text-green-600">{score}%</p>
          </div>

          <DigitalPermit />
          <FeedbackSection />
          <SharePermitSection />
        </div>
      </div>
    </div>
  );
}