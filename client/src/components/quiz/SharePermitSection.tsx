import { Button } from '../ui/button';

export function SharePermitSection() {
  const handleShareWhatsApp = () => {
    const message = encodeURIComponent('Saya telah menyelesaikan Safety Induction Quiz dan mendapatkan Digital Permit!');
    const whatsappUrl = `https://wa.me/?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleShareEmail = () => {
    const subject = encodeURIComponent('Digital Permit - Safety Induction Quiz');
    const body = encodeURIComponent('Saya telah menyelesaikan Safety Induction Quiz dan mendapatkan Digital Permit!');
    const emailUrl = `mailto:?subject=${subject}&body=${body}`;
    window.open(emailUrl, '_blank');
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Bagikan Permit</h2>

      <div className="flex space-x-3">
        <Button 
          onClick={handleShareWhatsApp}
          className="flex-1 h-12 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center space-x-2"
        >
          <span>📱</span>
          <span>WhatsApp</span>
        </Button>
        <Button
          onClick={handleShareEmail}
          variant="outline"
          className="flex-1 h-12 text-blue-600 border-blue-600 hover:bg-blue-50 flex items-center justify-center space-x-2"
        >
          <span>📧</span>
          <span>Email</span>
        </Button>
      </div>
    </div>
  );
}