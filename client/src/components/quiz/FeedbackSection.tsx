import { useState } from 'react';
import { Button } from '../ui/button';

export function FeedbackSection() {
  const [rating, setRating] = useState(3);
  const [comment, setComment] = useState('');

  const handleSubmitFeedback = () => {
    console.log('Feedback submitted:', { rating, comment });
    // TODO: Implement feedback submission
  };

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Feedback</h2>

      {/* Star Rating */}
      <div className="flex space-x-1 mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => setRating(star)}
            className={`text-2xl ${
              star <= rating ? 'text-orange-400' : 'text-gray-300'
            } hover:text-orange-500 transition-colors`}
          >
            ★
          </button>
        ))}
      </div>

      <textarea
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        placeholder="Tinggalkan Komentar..."
        className="w-full h-24 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
      />

      <Button 
        onClick={handleSubmitFeedback}
        className="w-full h-12 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors mt-4"
      >
        Submit Feedback
      </Button>
    </div>
  );
}