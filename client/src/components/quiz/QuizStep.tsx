import { But<PERSON> } from '../ui/button';
import { PAMLogo } from '../ui/PAMLogo';
import { LogOut } from 'lucide-react';
import { Question } from '../../types/quiz.types';

interface QuizStepProps {
  question: Question;
  currentQuestion: number;
  totalQuestions: number;
  selectedAnswer: number | null;
  onAnswerSelect: (answerIndex: number) => void;
  onNextQuestion: () => void;
  onLogout: () => void;
}

export function QuizStep({
  question,
  currentQuestion,
  totalQuestions,
  selectedAnswer,
  onAnswerSelect,
  onNextQuestion,
  onLogout
}: QuizStepProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 pt-8 pb-6 px-6">
          <div className="flex justify-between items-start">
            <PAMLogo width={90} height={90} />
            <Button
              onClick={onLogout}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2 text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              <LogOut className="h-4 w-4" />
              <span>Logout</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-6 pb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">Visitor Safety Quiz</h1>

          {/* Question */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-6">
              Pertanyaan {currentQuestion + 1}: {question.question}
            </h2>

            <div className="space-y-4">
              {question.options.map((option, index) => (
                <label
                  key={index}
                  className={`flex items-start space-x-3 cursor-pointer p-4 rounded-lg border-2 transition-colors ${
                    selectedAnswer === index
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="answer"
                    value={index}
                    checked={selectedAnswer === index}
                    onChange={() => onAnswerSelect(index)}
                    className="mt-1 h-4 w-4 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="text-gray-900 leading-relaxed">{option}</span>
                </label>
              ))}
            </div>
          </div>

          <Button
            onClick={onNextQuestion}
            disabled={selectedAnswer === null}
            className="w-full h-14 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-300 text-white font-medium rounded-lg transition-colors text-base"
          >
            {currentQuestion < totalQuestions - 1 ? 'Pertanyaan Selanjutnya' : 'Selesai Quiz'}
          </Button>
        </div>
      </div>
    </div>
  );
}