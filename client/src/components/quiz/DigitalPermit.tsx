import { Button } from '../ui/button';

export function DigitalPermit() {
  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">Digital Permit</h2>
      
      {/* QR Code */}
      <div className="bg-white p-6 rounded-lg mb-6 text-center">
        <div className="w-40 h-40 mx-auto bg-black mb-4 flex items-center justify-center relative">
          <div className="absolute inset-2 bg-white"></div>
          <div className="absolute inset-4 bg-black grid grid-cols-8 gap-px">
            {/* QR Code pattern */}
            {Array.from({ length: 64 }, (_, i) => (
              <div
                key={i}
                className={`${
                  [0, 1, 2, 5, 6, 7, 8, 14, 16, 22, 24, 30, 32, 33, 34, 37, 38, 39, 40, 46, 48, 54, 56, 57, 58, 61, 62, 63].includes(i)
                    ? 'bg-black'
                    : 'bg-white'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="flex space-x-3 mb-6">
        <Button
          variant="outline"
          className="flex-1 h-12 text-blue-600 border-blue-600 hover:bg-blue-50"
        >
          Download PDF
        </Button>
        <Button
          variant="outline"
          className="flex-1 h-12 text-blue-600 border-blue-600 hover:bg-blue-50"
        >
          Download JPG
        </Button>
      </div>
    </div>
  );
}