import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';

export function HeatmapCard() {
  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-gray-900">
          Heatmap Pengunjung
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full bg-gradient-to-br from-blue-100 via-blue-300 to-red-400 rounded-lg relative overflow-hidden">
          {/* Simulated heatmap overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-500/30 to-transparent"></div>
          <div className="absolute top-1/3 left-1/2 w-20 h-20 bg-red-500/50 rounded-full blur-xl"></div>
          <div className="absolute top-2/3 left-1/3 w-16 h-16 bg-orange-500/40 rounded-full blur-lg"></div>
          <div className="absolute top-1/2 right-1/3 w-12 h-12 bg-yellow-500/30 rounded-full blur-md"></div>
          
          {/* Map-like grid overlay */}
          <div className="absolute inset-0 opacity-20">
            <svg width="100%" height="100%" className="text-gray-600">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
          
          {/* Location markers */}
          <div className="absolute top-1/3 left-1/2 w-3 h-3 bg-red-600 rounded-full border-2 border-white shadow-lg"></div>
          <div className="absolute top-2/3 left-1/3 w-2 h-2 bg-orange-600 rounded-full border border-white shadow-md"></div>
          <div className="absolute top-1/2 right-1/3 w-2 h-2 bg-yellow-600 rounded-full border border-white shadow-md"></div>
        </div>
      </CardContent>
    </Card>
  );
}
