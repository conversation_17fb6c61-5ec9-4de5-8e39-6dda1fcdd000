import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import { Calendar, Filter } from 'lucide-react';

type VisitorData = {
  id: string;
  name: string;
  company: string;
  type: 'PAM' | 'SMA' | 'IBM';
  status: 'approved' | 'pending' | 'rejected';
  date: string;
  location?: {
    lat: number;
    lng: number;
  };
};

interface RecentVisitorsProps {
  visitors: VisitorData[];
}

export function RecentVisitors({ visitors }: RecentVisitorsProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Disetujui</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><PERSON><PERSON><PERSON></Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const colors = {
      PAM: 'bg-orange-500 text-white hover:bg-orange-500',
      SMA: 'bg-blue-500 text-white hover:bg-blue-500',
      IBM: 'bg-purple-500 text-white hover:bg-purple-500'
    };
    
    return (
      <Badge className={colors[type as keyof typeof colors] || 'bg-gray-500 text-white'}>
        {type}
      </Badge>
    );
  };

  return (
    <Card className="bg-white border border-gray-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900">
            Pengunjung Baru-baru Ini
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Select>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Tipe Pengunjung" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua</SelectItem>
                <SelectItem value="PAM">PAM</SelectItem>
                <SelectItem value="SMA">SMA</SelectItem>
                <SelectItem value="IBM">IBM</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Filter by date
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-600">Nama</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Perusahaan</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Tipe</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">Tanggal</th>
              </tr>
            </thead>
            <tbody>
              {visitors.map((visitor) => (
                <tr key={visitor.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 text-gray-900">{visitor.name}</td>
                  <td className="py-3 px-4 text-gray-600">{visitor.company}</td>
                  <td className="py-3 px-4">{getTypeBadge(visitor.type)}</td>
                  <td className="py-3 px-4">{getStatusBadge(visitor.status)}</td>
                  <td className="py-3 px-4 text-gray-600">
                    {new Date(visitor.date).toLocaleDateString('id-ID')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}
