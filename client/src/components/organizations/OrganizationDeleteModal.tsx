import { Mo<PERSON> } from "../ui/modal";
import { <PERSON><PERSON> } from "../ui/button";
import { Trash2 } from "lucide-react";
import { Organization } from "../../types/auth.types";

interface OrganizationDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (id: string) => Promise<boolean>;
  organization: Organization | null;
  loading: boolean;
  error: string | null;
}

export function OrganizationDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  organization,
  loading,
  error,
}: OrganizationDeleteModalProps) {
  const handleConfirm = async () => {
    if (!organization) return;

    const success = await onConfirm(organization.id);
    if (success) {
      onClose();
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (!organization) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Hapus Organisasi">
      <div className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex items-center gap-3 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex-shrink-0">
            <Trash2 className="h-6 w-6 text-red-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">
              Konfirmasi Penghapusan
            </h3>
            <p className="text-sm text-red-700 mt-1">
              Apakah Anda yakin ingin menghapus organisasi{" "}
              <strong>"{organization.name}"</strong>?
            </p>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Peringatan
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Tindakan ini tidak dapat dibatalkan. Semua data yang terkait
                  dengan organisasi ini akan dihapus secara permanen.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            onClick={handleConfirm}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {loading ? "Menghapus..." : "Ya, Hapus"}
          </Button>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Batal
          </Button>
        </div>
      </div>
    </Modal>
  );
}
