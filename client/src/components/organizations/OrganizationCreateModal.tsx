import { useState, useEffect } from 'react';
import { Modal } from '../ui/modal';
import { Button } from '../ui/button';
import { Input } from '../ui/input';

interface OrganizationFormData {
  name: string;
  slug: string;
}

interface OrganizationCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: OrganizationFormData) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

export function OrganizationCreateModal({
  isOpen,
  onClose,
  onSubmit,
  loading,
  error
}: OrganizationCreateModalProps) {
  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    slug: ''
  });

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim()
    }));
  };

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({ name: '', slug: '' });
    }
  }, [isOpen]);

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.slug.trim()) {
      return;
    }

    const success = await onSubmit({
      name: formData.name.trim(),
      slug: formData.slug.trim()
    });

    if (success) {
      onClose();
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Tambah Organisasi"
    >
      <div className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nama Organisasi *
          </label>
          <Input
            type="text"
            value={formData.name}
            onChange={(e) => handleNameChange(e.target.value)}
            placeholder="Masukkan nama organisasi"
            disabled={loading}
            autoFocus
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Slug *
          </label>
          <Input
            type="text"
            value={formData.slug}
            onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
            placeholder="slug-organisasi"
            disabled={loading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Slug akan digunakan sebagai URL organisasi
          </p>
        </div>
        
        <div className="flex gap-3 pt-4">
          <Button
            onClick={handleSubmit}
            disabled={loading || !formData.name.trim() || !formData.slug.trim()}
            className="bg-orange-500 hover:bg-orange-600 text-white"
          >
            {loading ? 'Menyimpan...' : 'Simpan'}
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Batal
          </Button>
        </div>
      </div>
    </Modal>
  );
}