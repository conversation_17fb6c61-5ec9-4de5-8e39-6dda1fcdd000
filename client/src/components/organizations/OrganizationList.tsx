import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Edit, Trash2 } from 'lucide-react';
import { Organization } from '../../types/auth.types';
import { usePagination } from '../../hooks/usePagination';

interface OrganizationListProps {
  organizations: Organization[];
  loading: boolean;
  error: string | null;
  onEdit: (organization: Organization) => void;
  onDelete: (organization: Organization) => void;
  pageSize: number;
  onPageSizeChange: (size: number) => void;
}

export function OrganizationList({
  organizations,
  loading,
  error,
  onEdit,
  onDelete,
  pageSize,
  onPageSizeChange
}: OrganizationListProps) {
  const {
    currentPage,
    totalPages,
    startIndex,
    paginatedData,
    setPageSize,
    goToNextPage,
    goToPreviousPage,
    canGoNext,
    canGoPrevious
  } = usePagination({
    data: organizations,
    initialPageSize: pageSize
  });

  // Update page size when prop changes
  React.useEffect(() => {
    setPageSize(pageSize);
  }, [pageSize, setPageSize]);

  // Notify parent of page size changes
  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    onPageSizeChange(newSize);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat data organisasi...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (organizations.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">Belum ada organisasi yang terdaftar</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Page Size Selector */}
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Tampilkan:</span>
        <select
          value={pageSize}
          onChange={(e) => handlePageSizeChange(Number(e.target.value))}
          className="border border-gray-300 rounded px-2 py-1 text-sm"
        >
          <option value={5}>5</option>
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
        </select>
        <span className="text-sm text-gray-600">per halaman</span>
      </div>

      {/* Organizations Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-4 font-medium text-gray-700">Nama Organisasi</th>
                  <th className="text-left p-4 font-medium text-gray-700">Slug</th>
                  <th className="text-left p-4 font-medium text-gray-700">Tanggal Dibuat</th>
                  <th className="text-center p-4 font-medium text-gray-700">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.map((organization) => (
                  <tr key={organization.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div className="font-medium text-gray-900">{organization.name}</div>
                    </td>
                    <td className="p-4">
                      <span className="text-sm text-gray-600">{organization.slug || '-'}</span>
                    </td>
                    <td className="p-4">
                      <span className="text-sm text-gray-600">
                        {(() => {
                          const date = new Date(organization.createdAt);
                          return isNaN(date.getTime()) ? '-' : date.toLocaleDateString('id-ID', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          });
                        })()}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(organization)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDelete(organization)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {organizations.length > 0 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-600">
            Menampilkan {startIndex + 1}-{Math.min(startIndex + pageSize, organizations.length)} dari {organizations.length} organisasi
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPreviousPage}
              disabled={!canGoPrevious}
            >
              Sebelumnya
            </Button>
            <span className="px-3 py-1 text-sm">
              Halaman {currentPage} dari {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={!canGoNext}
            >
              Selanjutnya
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}