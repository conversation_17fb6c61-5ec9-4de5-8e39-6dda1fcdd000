import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { getSuperAdminRoutes } from './routes/SuperAdminRoutes';
import { getAdminRoutes } from './routes/AdminRoutes';
import { getUserRoutes } from './routes/UserRoutes';
import { RoleBasedRedirect } from './components/routes/RoleBasedRedirect';
import { LoginForm } from './components/auth/LoginForm';
import { SignupForm } from './components/auth/SignupForm';


function App() {
  return (
    <Router>
      <Routes>
        {/* Default redirect - role-based for authenticated users (highest priority) */}
        <Route path="/" element={<RoleBasedRedirect />} />
        
        {/* Authentication routes */}
        <Route path="/login" element={<LoginForm />} />
        <Route path="/signup" element={<SignupForm />} />

        {/* SuperAdmin Routes */}
        {getSuperAdminRoutes()}

        {/* Admin Routes */}
        {getAdminRoutes()}

        {/* User Routes */}
        {getUserRoutes()}
        
        {/* Legacy route redirects */}
        <Route path="/visitor-safety-quiz" element={<Navigate to="/user/quiz" replace />} />

        {/* Catch-all route for undefined paths - redirect to login */}
        <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
    </Router>
  );
}

export default App