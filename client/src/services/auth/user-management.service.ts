import type { AuthResponse } from '../../types/auth.types';
import { authClient } from '../../lib/auth-client';

/**
 * User Management service
 * Handles user CRUD operations, role management, and user administration
 */
export class UserManagementService {
  /**
   * Set user role (admin only)
   * @param userId - User ID to change role for
   * @param role - New role to assign
   * @returns Promise<AuthResponse>
   */
  async setUserRole(userId: string, role: string | string[]): Promise<AuthResponse> {
    try {
      console.log('🔍 Setting user role:', { userId, role });

      // Handle superadmin role with custom endpoint
      if (role === 'superadmin' || (Array.isArray(role) && role.includes('superadmin'))) {
        const response = await fetch('/api/admin/set-superadmin-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ userId, role }),
        });

        const result = await response.json();
        if (!response.ok || !result.success) {
          console.error('❌ Set superadmin role error:', result.error);
          return { success: false, error: result.error || 'Failed to set superadmin role' };
        }
        return { success: true };
      }

      const result = await authClient.admin.setRole({
         userId,
         role: role as "user" | "admin" | ("user" | "admin")[],
       });

      console.log('🔍 Set role result:', result);

      if (result.error) {
        console.error('❌ Set role error:', result.error);
        return { success: false, error: result.error.message || 'Failed to set user role' };
      }

      console.log('✅ Role set successfully:', result);
      
      return { success: true };
    } catch (error) {
      console.error('❌ Set role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Create a new user (admin only)
   * @param email - User email
   * @param password - User password
   * @param name - User name
   * @param role - User role (optional, defaults to 'user')
   * @param organizationId - Organization ID (optional, for admin/user roles)
   * @param customRoleId - Custom role ID (optional, for custom roles)
   * @returns Promise<AuthResponse>
   */
  async createUser(email: string, password: string, name: string, role?: 'user' | 'admin' | 'superadmin' | 'custom' | ('user' | 'admin' | 'superadmin' | 'custom')[], organizationId?: string, customRoleId?: string): Promise<AuthResponse & { data?: any }> {
    try {
      console.log('🔍 Creating user:', { email, name, role, organizationId, customRoleId });

      // Handle custom role with custom endpoint
      if (role === 'custom' || (Array.isArray(role) && role.includes('custom'))) {
        const response = await fetch('/api/admin/create-user-with-custom-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password, name, role, organizationId, customRoleId }),
        });

        const result = await response.json();
        if (!response.ok || !result.success) {
          console.error('❌ Create user with custom role error:', result.error);
          return { success: false, error: result.error || 'Failed to create user with custom role' };
        }
        return { success: true, data: result.data };
      }

      // Handle superadmin role with custom endpoint
      if (role === 'superadmin' || (Array.isArray(role) && role.includes('superadmin'))) {
        const response = await fetch('/api/admin/create-superadmin-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password, name, role }),
        });

        const result = await response.json();
        if (!response.ok || !result.success) {
          console.error('❌ Create superadmin user error:', result.error);
          return { success: false, error: result.error || 'Failed to create superadmin user' };
        }
        return { success: true, data: result.data };
      }

      // Handle admin/user roles with organization assignment
      if ((role === 'admin' || role === 'user') && organizationId) {
        const response = await fetch('/api/admin/create-user-with-org', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password, name, role, organizationId }),
        });

        const result = await response.json();
        if (!response.ok || !result.success) {
          console.error('❌ Create user with organization error:', result.error);
          return { success: false, error: result.error || 'Failed to create user with organization' };
        }
        return { success: true, data: result.data };
      }

      // Fallback to default Better Auth for simple user creation
      const result = await authClient.admin.createUser({
        email,
        password,
        name,
        role: (role as 'user' | 'admin') || 'user',
      });

      if (result.error) {
        console.error('❌ Create user error:', result.error);
        return { success: false, error: result.error.message || 'Failed to create user' };
      }

      console.log('✅ User created successfully:', result.data);
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ Create user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * List all users (admin only)
   * @param searchValue - Search query (optional)
   * @param limit - Number of users to return (optional)
   * @param offset - Number of users to skip (optional)
   * @returns Promise<any[]>
   */
  async listUsers(searchValue?: string, limit?: number, offset?: number): Promise<any[]> {
    try {
      console.log('🔍 Listing users:', { searchValue, limit, offset });

      const query = searchValue ? {
        searchValue,
        searchField: 'email' as const,
        limit,
        offset,
      } : {
        limit,
        offset,
      };

      const result = await authClient.admin.listUsers({ query });

      if (result.error) {
        console.error('❌ List users error:', result.error);
        return [];
      }

      console.log('✅ Users listed successfully:', result.data?.users?.length || 0);
      return result.data?.users || [];
    } catch (error) {
      console.error('❌ List users exception:', error);
      return [];
    }
  }

  /**
   * Update user password (admin only)
   * @param userId - User ID
   * @param newPassword - New password
   * @returns Promise<AuthResponse>
   */
  async updateUserPassword(userId: string, newPassword: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating user password:', { userId });

      const result = await authClient.admin.setUserPassword({
        userId,
        newPassword,
      });

      if (result.error) {
        console.error('❌ Update user password error:', result.error);
        return { success: false, error: result.error.message || 'Failed to update user password' };
      }

      console.log('✅ User password updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Update user password exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update user data (admin only)
   * @param userId - User ID
   * @param data - User data to update
   * @returns Promise<AuthResponse>
   */
  async updateUser(userId: string, data: { name?: string; email?: string; role?: 'user' | 'admin' | 'superadmin' | 'custom' | ('user' | 'admin' | 'superadmin' | 'custom')[]; organizationId?: string; customRoleId?: string }): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating user:', { userId, data });

      // Handle custom role with custom endpoint
      if (data.role === 'custom' && data.customRoleId) {
        const response = await fetch('/api/admin/update-user-with-custom-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ userId, customRoleId: data.customRoleId, name: data.name, email: data.email }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Update user with custom role HTTP error:', errorText);
          return { success: false, error: `HTTP error! status: ${response.status} - ${errorText}` };
        }

        const result = await response.json();
        if (!result.success) {
          console.error('❌ Update user with custom role error:', result.error);
          return { success: false, error: result.error || 'Failed to update user with custom role' };
        }
        console.log('✅ Update user with custom role success:', result);
        return { success: true };
      }

      // Handle superadmin role with custom endpoint
      if (data.role === 'superadmin' || (Array.isArray(data.role) && data.role.includes('superadmin'))) {
        const response = await fetch('/api/admin/update-superadmin-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ userId, data }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server response:', errorText);
          return { success: false, error: `HTTP error! status: ${response.status} - ${errorText}` };
        }

        const result = await response.json();
        if (!result.success) {
          console.error('❌ Update superadmin user error:', result.error);
          return { success: false, error: result.error || 'Failed to update superadmin user' };
        }
        console.log('✅ Update superadmin user success:', result);
        return { success: true };
      }

      // Handle role updates first
      if (data.role && typeof data.role === 'string' && ['admin', 'user'].includes(data.role)) {
        const response = await fetch('/api/admin/update-user-role', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ 
            userId, 
            role: data.role, 
            organizationId: data.organizationId 
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server response:', errorText);
          return { success: false, error: `HTTP error! status: ${response.status} - ${errorText}` };
        }

        const result = await response.json();
        if (!result.success) {
          console.error('❌ Update user role error:', result.error);
          return { success: false, error: result.error ?? 'Failed to update user role' };
        }
        console.log('✅ Update user role success:', result);
      }

      // Handle name/email updates (can be combined with role updates)
      if (data.name || data.email) {
        const response = await fetch('/api/admin/update-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ 
            userId, 
            name: data.name,
            email: data.email
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Server response:', errorText);
          return { success: false, error: `HTTP error! status: ${response.status} - ${errorText}` };
        }

        const result = await response.json();
        if (!result.success) {
          console.error('❌ Update user data error:', result.error);
          return { success: false, error: result.error ?? 'Failed to update user data' };
        }
        console.log('✅ Update user data success:', result);
      }

      return { success: true };

      // Fallback to default Better Auth for other updates
      const result = await authClient.admin.updateUser({
        userId,
        data: {
          ...data,
          role: data.role as 'user' | 'admin' | ('user' | 'admin')[] | undefined
        },
      });

      if (result.error) {
        console.error('❌ Update user error:', result.error);
        return { success: false, error: result.error?.message || 'Failed to update user' };
      }

      console.log('✅ User updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Update user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Delete user (admin only)
   * @param userId - User ID to delete
   * @returns Promise<AuthResponse>
   */
  async deleteUser(userId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Deleting user:', { userId });

      const result = await authClient.admin.removeUser({
        userId,
      });

      if (result.error) {
        console.error('❌ Delete user error:', result.error);
        return { success: false, error: result.error.message || 'Failed to delete user' };
      }

      console.log('✅ User deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Delete user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
}