import type { User, AuthResponse } from '../../types/auth.types';
import { authClient } from '../../lib/auth-client';

/**
 * Core Authentication service using Better Auth client
 * Handles login, signup, logout, and authentication state checking
 */
export class CoreAuthService {
  /**
   * Check current authentication state from server
   * @returns Promise<User | null>
   */
  async checkAuthState(): Promise<User | null> {
    try {
      console.log('🔍 Checking auth state...');

      const session = await authClient.getSession();
      console.log('🔍 Session data:', session);

      if (session?.data?.user) {
         console.log('✅ User authenticated:', session.data.user.email);
         return {
           ...session.data.user,
           createdAt: session.data.user.createdAt.toString(),
           updatedAt: session.data.user.updatedAt.toString()
         } as User;
       } else {
         console.log('❌ No valid session');
         return null;
       }
    } catch (error) {
      console.error('❌ Auth check failed:', error);
      return null;
    }
  }

  /**
   * <PERSON>gin user with email and password
   * @param email - User email
   * @param password - User password
   * @returns Promise<AuthResponse>
   */
  async login(email: string, password: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Attempting login with:', { email });

      const result = await authClient.signIn.email({
        email,
        password,
      });

      console.log('🔍 Login result:', result);
      
      if (result.error) {
        console.error('❌ Login error:', result.error);
        return { success: false, error: result.error.message || 'Login failed' };
      }
      
      return { success: true };
    } catch (error) {
      console.error('❌ Login exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Register new user
   * @param email - User email
   * @param password - User password
   * @param name - User name
   * @returns Promise<AuthResponse>
   */
  async signup(email: string, password: string, name: string): Promise<AuthResponse> {
    try {
      const result = await authClient.signUp.email({
        email,
        password,
        name,
      });

      if (result.error) {
        console.error('Signup error:', result.error);
        return { success: false, error: result.error.message || 'Signup failed' };
      }

      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Logout current user
   * @returns Promise<void>
   */
  async logout(): Promise<void> {
    try {
      await authClient.signOut();
      console.log('✅ User logged out');
    } catch (error) {
      console.error('Logout error:', error);
      // Still proceed with logout even if server logout fails
    }
  }
}