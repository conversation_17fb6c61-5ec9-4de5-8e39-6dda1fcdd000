// Main AuthService facade that combines all auth-related services
export { AuthService } from './facade.service';

// Individual services for direct access if needed
export { CoreAuthService } from './auth.service';
export { UserManagementService } from './user-management.service';
export { OrganizationService } from './organization.service';
export { OrganizationMembershipService } from './organization-membership.service';
export { AdminService } from './admin.service';

// Create and export a default instance for backward compatibility
import { AuthService } from './facade.service';
export const authService = new AuthService();

// Export default as the main AuthService instance
export default authService;