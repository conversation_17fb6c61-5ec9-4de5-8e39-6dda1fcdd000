import type { AuthResponse, User } from '../../types/auth.types';
import { authClient } from '../../lib/auth-client';

/**
 * Organization Membership service
 * Handles organization membership operations like adding/removing members
 */
export class OrganizationMembershipService {
  /**
   * Add user to organization
   * @param userId - User ID to add
   * @param organizationId - Organization ID
   * @param role - Role to assign (default: 'member')
   * @returns Promise<AuthResponse>
   */
  async addUserToOrganization(userId: string, organizationId: string, role: string = 'member'): Promise<AuthResponse> {
    try {
      console.log('🔍 Adding user to organization:', { userId, organizationId, role });

      const response = await fetch('/api/organization/add-member', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, organizationId, role }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Add user to organization error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to add user to organization' };
      }

      const data = await response.json();
      console.log('✅ User added to organization successfully:', data);
      return { success: true };
    } catch (error) {
      console.error('❌ Add user to organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Remove user from organization
   * @param userId - User ID to remove
   * @param organizationId - Organization ID
   * @returns Promise<AuthResponse>
   */
  async removeUserFromOrganization(userId: string, organizationId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Removing user from organization:', { userId, organizationId });

      const response = await fetch('/api/organization/remove-member', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, organizationId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Remove user from organization error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to remove user from organization' };
      }

      const data = await response.json();
      console.log('✅ User removed from organization successfully:', data);
      return { success: true };
    } catch (error) {
      console.error('❌ Remove user from organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get organization members
   * @param organizationId - Organization ID
   * @returns Promise<User[]>
   */
  async getOrganizationMembers(organizationId: string): Promise<User[]> {
    try {
      console.log('🔍 Getting organization members for ID:', organizationId);
      console.log('🔍 Raw ID:', JSON.stringify(organizationId));
      
      const cleanedId = organizationId?.trim();
      console.log('🔍 Cleaned ID:', JSON.stringify(cleanedId));
      
      const response = await fetch(`/api/admin/organization-members/${cleanedId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`Failed to get organization members: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('✅ API Result:', result);
      
      if (result.success && result.members) {
        console.log('✅ Members found:', result.members);
        return result.members;
      } else {
        console.log('❌ No members found in result');
        return [];
      }
    } catch (error) {
      console.error('❌ Error in getOrganizationMembers:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  /**
   * Invite user to organization
   * @param email - Email of user to invite
   * @param organizationId - Organization ID
   * @param role - Role to assign (default: 'member')
   * @returns Promise<AuthResponse>
   */
  async inviteToOrganization(email: string, organizationId: string, role: 'admin' | 'member' | 'owner' = 'member'): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.inviteMember({
        email,
        organizationId,
        role,
      });

      if (result.error) {
        console.error('❌ Invite to organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to invite user to organization' };
      }

      console.log('✅ User invited to organization successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Invite to organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Accept organization invitation
   * @param invitationId - Invitation ID
   * @returns Promise<AuthResponse>
   */
  async acceptInvitation(invitationId: string): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.acceptInvitation({
        invitationId,
      });

      if (result.error) {
        console.error('❌ Accept invitation error:', result.error);
        return { success: false, error: result.error.message || 'Failed to accept invitation' };
      }

      console.log('✅ Invitation accepted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Accept invitation exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Reject organization invitation
   * @param invitationId - Invitation ID
   * @returns Promise<AuthResponse>
   */
  async rejectInvitation(invitationId: string): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.rejectInvitation({
        invitationId,
      });

      if (result.error) {
        console.error('❌ Reject invitation error:', result.error);
        return { success: false, error: result.error.message || 'Failed to reject invitation' };
      }

      console.log('✅ Invitation rejected successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Reject invitation exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Leave organization
   * @param organizationId - Organization ID to leave
   * @returns Promise<AuthResponse>
   */
  async leaveOrganization(organizationId: string): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.leave({
        organizationId,
      });

      if (result.error) {
        console.error('❌ Leave organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to leave organization' };
      }

      console.log('✅ Left organization successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Leave organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Remove member from organization
   * @param memberIdOrEmail - Member ID or email to remove
   * @param organizationId - Organization ID
   * @returns Promise<AuthResponse>
   */
  async removeMember(memberIdOrEmail: string, organizationId: string): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.removeMember({
        memberIdOrEmail,
        organizationId,
      });

      if (result.error) {
        console.error('❌ Remove member error:', result.error);
        return { success: false, error: result.error.message || 'Failed to remove member' };
      }

      console.log('✅ Member removed successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Remove member exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update member role in organization
   * @param memberId - Member ID
   * @param organizationId - Organization ID
   * @param role - New role
   * @returns Promise<AuthResponse>
   */
  async updateMemberRole(memberId: string, organizationId: string, role: 'admin' | 'member' | 'owner'): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.updateMemberRole({
        memberId,
        organizationId,
        role,
      });

      if (result.error) {
        console.error('❌ Update member role error:', result.error);
        return { success: false, error: result.error.message || 'Failed to update member role' };
      }

      console.log('✅ Member role updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Update member role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
}