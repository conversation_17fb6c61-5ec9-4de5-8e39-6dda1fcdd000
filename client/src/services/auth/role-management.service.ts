import type { AuthResponse } from '../../types/auth.types';

/**
 * Role with organization and user information
 */
export interface RoleWithDetails {
  id: string;
  role: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  organization: {
    id: string;
    name: string;
  };
  createdAt: string;
}

/**
 * Unique role with priority (Member > User)
 */
export interface UniqueRole {
  id: string;
  role: string;
  customRoleName?: string;
  organizationName: string;
  organizationId?: string;
  userId: string;
  userName?: string;
  userEmail?: string;
  createdAt: string;
  type: 'member' | 'user';
}

/**
 * Create role data
 */
export interface CreateRoleData {
  userId: string;
  organizationId: string;
  role: string;
}

/**
 * Update role data
 */
export interface UpdateRoleData {
  role: string;
}

/**
 * Custom role permission data
 * Note: Backend uses 'module' field, frontend uses 'moduleType'
 */
export interface CustomRolePermission {
  moduleType: string;
  module?: string; // Backend field
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

/**
 * Custom role creation data
 */
export interface CreateCustomRoleData {
  name: string;
  description: string;
  organizationId: string;
  permissions: CustomRolePermission[];
}

/**
 * Custom role with permissions
 */
export interface CustomRoleWithPermissions {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  organizationName?: string;
  permissions: CustomRolePermission[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Update custom role data
 */
export interface UpdateCustomRoleData {
  name?: string;
  description?: string;
  permissions?: CustomRolePermission[];
}

/**
 * Role Management service
 * Handles role CRUD operations within organizations (Member model)
 */
export class RoleManagementService {
  private baseUrl = '/api';

  /**
   * Get unique roles with priority (Member > User)
   * @param searchValue - Search query (optional)
   * @param page - Page number (optional)
   * @param limit - Number of roles to return (optional)
   * @returns Promise<UniqueRole[]>
   */
  async listUniqueRoles(searchValue?: string, page?: number, limit?: number): Promise<UniqueRole[]> {
    try {
      console.log('🔍 Listing unique roles:', { searchValue, page, limit });

      const params = new URLSearchParams();
      if (searchValue) params.append('search', searchValue);
      if (page) params.append('page', page.toString());
      if (limit) params.append('limit', limit.toString());

      const response = await fetch(`${this.baseUrl}/admin/unique-roles?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ List unique roles error:', result.error);
        return [];
      }

      console.log('✅ Unique roles listed successfully:', result.roles?.length || 0);
      return result.roles || [];
    } catch (error) {
      console.error('❌ List unique roles exception:', error);
      return [];
    }
  }

  /**
   * Get all roles with user and organization details
   * @param searchValue - Search query (optional)
   * @param limit - Number of roles to return (optional)
   * @param offset - Number of roles to skip (optional)
   * @returns Promise<RoleWithDetails[]>
   */
  async listRoles(searchValue?: string, limit?: number, offset?: number): Promise<RoleWithDetails[]> {
    try {
      console.log('🔍 Listing roles:', { searchValue, limit, offset });

      const params = new URLSearchParams();
      if (searchValue) params.append('search', searchValue);
      if (limit) params.append('limit', limit.toString());
      if (offset) params.append('offset', offset.toString());

      const response = await fetch(`${this.baseUrl}/admin/roles?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ List roles error:', result.error);
        return [];
      }

      console.log('✅ Roles listed successfully:', result.roles?.length || 0);
      return result.roles || [];
    } catch (error) {
      console.error('❌ List roles exception:', error);
      return [];
    }
  }

  /**
   * Create a new role assignment (add user to organization with role)
   * @param roleData - Role data to create
   * @returns Promise<AuthResponse>
   */
  async createRole(roleData: CreateRoleData): Promise<AuthResponse & { data?: any }> {
    try {
      console.log('🔍 Creating role:', roleData);

      const response = await fetch(`${this.baseUrl}/admin/roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(roleData),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Create role error:', result.error);
        return { success: false, error: result.error || 'Failed to create role' };
      }

      console.log('✅ Role created successfully:', result.data);
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ Create role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update a role assignment
   * @param roleId - Role ID (Member ID)
   * @param roleData - Role data to update
   * @returns Promise<AuthResponse>
   */
  async updateRole(roleId: string, roleData: UpdateRoleData): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating role:', { roleId, roleData });

      const response = await fetch(`${this.baseUrl}/admin/roles/${roleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(roleData),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Update role error:', result.error);
        return { success: false, error: result.error || 'Failed to update role' };
      }

      console.log('✅ Role updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Update role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Delete a role assignment (remove user from organization)
   * @param roleId - Role ID (Member ID)
   * @returns Promise<AuthResponse>
   */
  async deleteRole(roleId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Deleting role:', { roleId });

      const response = await fetch(`${this.baseUrl}/admin/roles/${roleId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Delete role error:', result.error);
        return { success: false, error: result.error || 'Failed to delete role' };
      }

      console.log('✅ Role deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Delete role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get available users (users not in any organization or can be added to multiple orgs)
   * @returns Promise<any[]>
   */
  async getAvailableUsers(): Promise<any[]> {
    try {
      console.log('🔍 Getting available users');

      const response = await fetch(`${this.baseUrl}/admin/available-users`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Get available users error:', result.error);
        return [];
      }

      console.log('✅ Available users retrieved successfully:', result.users?.length || 0);
      return result.users || [];
    } catch (error) {
      console.error('❌ Get available users exception:', error);
      return [];
    }
  }

  /**
   * Get available organizations
   * @returns Promise<any[]>
   */
  async getAvailableOrganizations(): Promise<any[]> {
    try {
      console.log('🔍 Getting available organizations');

      const response = await fetch(`${this.baseUrl}/admin/available-organizations`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Get available organizations error:', result.error);
        return [];
      }

      console.log('✅ Available organizations retrieved successfully:', result.organizations?.length || 0);
      return result.organizations || [];
    } catch (error) {
      console.error('❌ Get available organizations exception:', error);
      return [];
    }
  }

  /**
   * Get all custom roles
   * @param searchValue - Search query (optional)
   * @param page - Page number (optional)
   * @param limit - Items per page (optional)
   * @returns Promise<{ customRoles: CustomRoleWithPermissions[], total: number, totalPages: number }>
   */
  async listCustomRoles(searchValue?: string, page?: number, limit?: number): Promise<{ customRoles: CustomRoleWithPermissions[], total: number, totalPages: number }> {
    try {
      console.log('🔍 Listing custom roles:', { searchValue, page, limit });

      const params = new URLSearchParams();
      if (searchValue) params.append('search', searchValue);
      if (page) params.append('page', page.toString());
      if (limit) params.append('limit', limit.toString());

      const response = await fetch(`${this.baseUrl}/admin/custom-roles?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ List custom roles error:', result.error);
        return { customRoles: [], total: 0, totalPages: 0 };
      }

      console.log('✅ Custom roles listed successfully:', result.customRoles?.length || 0);
      return {
        customRoles: result.customRoles || [],
        total: result.total || 0,
        totalPages: result.totalPages || 0
      };
    } catch (error) {
      console.error('❌ List custom roles exception:', error);
      return { customRoles: [], total: 0, totalPages: 0 };
    }
  }

  /**
   * Get a custom role by ID
   * @param customRoleId - Custom role ID
   * @returns Promise<CustomRoleWithPermissions | null>
   */
  async getCustomRole(customRoleId: string): Promise<CustomRoleWithPermissions | null> {
    try {
      console.log('🔍 Getting custom role:', { customRoleId });

      const response = await fetch(`${this.baseUrl}/admin/custom-roles/${customRoleId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Get custom role error:', result.error);
        return null;
      }

      console.log('✅ Custom role retrieved successfully:', result.customRole);
      return result.customRole || null;
    } catch (error) {
      console.error('❌ Get custom role exception:', error);
      return null;
    }
  }

  /**
   * Create a custom role with permissions
   * @param customRoleData - Custom role data to create
   * @returns Promise<AuthResponse>
   */
  async createCustomRole(customRoleData: CreateCustomRoleData): Promise<AuthResponse & { data?: any }> {
    try {
      console.log('🔍 Creating custom role:', customRoleData);

      const response = await fetch(`${this.baseUrl}/admin/custom-roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(customRoleData),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Create custom role error:', result.error);
        return { success: false, error: result.error || 'Failed to create custom role' };
      }

      console.log('✅ Custom role created successfully:', result.data);
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ Create custom role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update a custom role
   * @param customRoleId - Custom role ID
   * @param customRoleData - Custom role data to update
   * @returns Promise<AuthResponse>
   */
  async updateCustomRole(customRoleId: string, customRoleData: UpdateCustomRoleData): Promise<AuthResponse & { data?: any }> {
    try {
      console.log('🔍 Updating custom role:', { customRoleId, customRoleData });

      const response = await fetch(`${this.baseUrl}/admin/custom-roles/${customRoleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(customRoleData),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Update custom role error:', result.error);
        return { success: false, error: result.error || 'Failed to update custom role' };
      }

      console.log('✅ Custom role updated successfully:', result.data);
      return { success: true, data: result.data };
    } catch (error) {
      console.error('❌ Update custom role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Delete a custom role
   * @param customRoleId - Custom role ID
   * @returns Promise<AuthResponse>
   */
  async deleteCustomRole(customRoleId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Deleting custom role:', { customRoleId });

      const response = await fetch(`${this.baseUrl}/admin/custom-roles/${customRoleId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        console.error('❌ Delete custom role error:', result.error);
        return { success: false, error: result.error || 'Failed to delete custom role' };
      }

      console.log('✅ Custom role deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Delete custom role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
}

// Export singleton instance
export const roleManagementService = new RoleManagementService();