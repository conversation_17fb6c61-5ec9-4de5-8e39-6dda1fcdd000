import type { AuthResponse, User } from '../../types/auth.types';
import { CoreAuthService } from './auth.service';

/**
 * Admin service
 * Handles admin-specific operations and superadmin functionality
 */
export class AdminService {
  private coreAuthService: CoreAuthService;

  constructor() {
    this.coreAuthService = new CoreAuthService();
  }

  /**
   * Update user role (admin/superadmin only)
   * @param userId - User ID to update
   * @param role - New role to assign
   * @param organizationId - Organization ID (required for role updates)
   * @returns Promise<AuthResponse>
   */
  async updateUserRole(userId: string, role: string, organizationId?: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating user role:', { userId, role, organizationId });

      const response = await fetch('/api/admin/update-user-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, role, organizationId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Update user role error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to update user role' };
      }

      const data = await response.json();
      console.log('✅ User role updated successfully:', data);
      return { success: true };
    } catch (error) {
      console.error('❌ Update user role exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get all users (admin/superadmin only)
   * @returns Promise<User[]>
   */
  async getAllUsers(): Promise<User[]> {
    try {
      console.log('🔍 Getting all users...');
      
      // Check if user is superadmin and use custom endpoint
      const currentUser = await this.coreAuthService.checkAuthState();
      if (currentUser?.role === 'superadmin') {
        const response = await fetch('/api/admin/all-users', {
          method: 'GET',
          credentials: 'include',
        });

        if (!response.ok) {
          console.error('❌ Get all users error:', response.statusText);
          return [];
        }

        const data = await response.json();
        console.log('✅ All users retrieved successfully (superadmin):', data.users?.length || 0);
        return data.users || [];
      }
      
      // For non-superadmin users, return empty array or handle differently
      console.log('❌ Access denied: Only superadmin can get all users');
      return [];
    } catch (error) {
      console.error('❌ Get all users exception:', error);
      return [];
    }
  }

  /**
   * Delete user (superadmin only)
   * @param userId - User ID to delete
   * @returns Promise<AuthResponse>
   */
  async deleteUser(userId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Deleting user:', userId);

      const response = await fetch('/api/admin/delete-user', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Delete user error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to delete user' };
      }

      await response.json();
      console.log('✅ User deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Delete user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Create user (admin/superadmin only)
   * @param userData - User data
   * @returns Promise<AuthResponse>
   */
  async createUser(userData: {
    name: string;
    email: string;
    password: string;
    role: string;
    organizationId?: string;
  }): Promise<AuthResponse> {
    try {
      console.log('🔍 Creating user:', { ...userData, password: '[HIDDEN]' });

      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Create user error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to create user' };
      }

      const data = await response.json();
      console.log('✅ User created successfully:', data.user);
      return { success: true };
    } catch (error) {
      console.error('❌ Create user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update user (admin/superadmin only)
   * @param userId - User ID to update
   * @param userData - User data to update
   * @returns Promise<AuthResponse>
   */
  async updateUser(userId: string, userData: {
    name?: string;
    email?: string;
    role?: string;
    organizationId?: string;
  }): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating user:', { userId, userData });

      const response = await fetch('/api/admin/update-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, ...userData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Update user error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to update user' };
      }

      const data = await response.json();
      console.log('✅ User updated successfully:', data);
      return { success: true };
    } catch (error) {
      console.error('❌ Update user exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Update user password (admin/superadmin only)
   * @param userId - User ID
   * @param newPassword - New password
   * @returns Promise<AuthResponse>
   */
  async updateUserPassword(userId: string, newPassword: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating user password for:', userId);

      const response = await fetch('/api/admin/update-user-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, newPassword }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Update user password error:', errorData.error);
        return { success: false, error: errorData.error || 'Failed to update user password' };
      }

      await response.json();
      console.log('✅ User password updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Update user password exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get system statistics (superadmin only)
   * @returns Promise<any>
   */
  async getSystemStats(): Promise<any> {
    try {
      console.log('🔍 Getting system statistics...');

      const response = await fetch('/api/admin/system-stats', {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        console.error('❌ Get system stats error:', response.statusText);
        return null;
      }

      const data = await response.json();
      console.log('✅ System stats retrieved successfully');
      return data.stats;
    } catch (error) {
      console.error('❌ Get system stats exception:', error);
      return null;
    }
  }

  /**
   * Check if current user has admin privileges
   * @returns Promise<boolean>
   */
  async isAdmin(): Promise<boolean> {
    try {
      const currentUser = await this.coreAuthService.checkAuthState();
      return currentUser?.role === 'admin' || currentUser?.role === 'superadmin';
    } catch (error) {
      console.error('❌ Check admin status exception:', error);
      return false;
    }
  }

  /**
   * Check if current user has superadmin privileges
   * @returns Promise<boolean>
   */
  async isSuperAdmin(): Promise<boolean> {
    try {
      const currentUser = await this.coreAuthService.checkAuthState();
      return currentUser?.role === 'superadmin';
    } catch (error) {
      console.error('❌ Check superadmin status exception:', error);
      return false;
    }
  }
}