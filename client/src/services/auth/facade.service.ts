import type { AuthResponse, User, Organization } from '../../types/auth.types';
import { CoreAuthService } from './auth.service';
import { UserManagementService } from './user-management.service';
import { OrganizationService } from './organization.service';
import { OrganizationMembershipService } from './organization-membership.service';
import { AdminService } from './admin.service';

/**
 * AuthService Facade
 * Combines all auth-related services into a single interface
 * Maintains backward compatibility with existing code
 */
export class AuthService {
  private coreAuth: CoreAuthService;
  private userManagement: UserManagementService;
  private organization: OrganizationService;
  private membership: OrganizationMembershipService;
  private admin: AdminService;

  constructor() {
    this.coreAuth = new CoreAuthService();
    this.userManagement = new UserManagementService();
    this.organization = new OrganizationService();
    this.membership = new OrganizationMembershipService();
    this.admin = new AdminService();
  }

  // Core Authentication Methods
  async checkAuthState() {
    return this.coreAuth.checkAuthState();
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    return this.coreAuth.login(email, password);
  }

  async signup(name: string, email: string, password: string): Promise<AuthResponse> {
    return this.coreAuth.signup(name, email, password);
  }

  async logout(): Promise<AuthResponse> {
    await this.coreAuth.logout();
    return { success: true };
  }

  // User Management Methods
  async setUserRole(userId: string, role: string): Promise<AuthResponse> {
    return this.userManagement.setUserRole(userId, role);
  }

  async createUser(email: string, password: string, name: string, role: 'admin' | 'user' | 'superadmin' | 'custom', organizationId?: string, customRoleId?: string): Promise<AuthResponse> {
    return this.userManagement.createUser(email, password, name, role, organizationId, customRoleId);
  }

  async listUsers(searchValue?: string, limit?: number, offset?: number): Promise<User[]> {
    return this.userManagement.listUsers(searchValue, limit, offset);
  }

  async updateUserPassword(userId: string, newPassword: string): Promise<AuthResponse> {
    return this.userManagement.updateUserPassword(userId, newPassword);
  }

  async updateUser(userId: string, userData: {
    name?: string;
    email?: string;
    role?: 'admin' | 'user' | 'superadmin' | 'custom';
    organizationId?: string;
    customRoleId?: string;
  }): Promise<AuthResponse> {
    return this.userManagement.updateUser(userId, userData);
  }

  async deleteUser(userId: string): Promise<AuthResponse> {
    return this.userManagement.deleteUser(userId);
  }

  // Organization Methods
  async createOrganization(name: string, slug: string, logo?: string): Promise<AuthResponse> {
    return this.organization.createOrganization(name, slug, logo);
  }

  async listOrganizations(): Promise<Organization[]> {
    return this.organization.listOrganizations();
  }

  async setActiveOrganization(organizationId: string): Promise<AuthResponse> {
    return this.organization.setActiveOrganization(organizationId);
  }

  async getUserOrganizations(userId: string): Promise<Organization[]> {
    return this.organization.getUserOrganizations(userId);
  }

  async getOrganizationBySlug(organizationSlug: string): Promise<Organization | null> {
    return this.organization.getOrganizationBySlug(organizationSlug);
  }

  async getAllOrganizations(): Promise<Organization[]> {
    return this.organization.getAllOrganizations();
  }

  async updateOrganization(organizationId: string, name: string, slug: string): Promise<AuthResponse> {
    return this.organization.updateOrganization(organizationId, name, slug);
  }

  async deleteOrganization(organizationId: string): Promise<AuthResponse> {
    return this.organization.deleteOrganization(organizationId);
  }

  // Organization Membership Methods
  async addUserToOrganization(userId: string, organizationId: string, role: string = 'member'): Promise<AuthResponse> {
    return this.membership.addUserToOrganization(userId, organizationId, role);
  }

  async removeUserFromOrganization(userId: string, organizationId: string): Promise<AuthResponse> {
    return this.membership.removeUserFromOrganization(userId, organizationId);
  }

  async getOrganizationMembers(organizationId: string): Promise<User[]> {
    return this.membership.getOrganizationMembers(organizationId);
  }

  async inviteToOrganization(email: string, organizationId: string, role: 'admin' | 'member' | 'owner' = 'member'): Promise<AuthResponse> {
    return this.membership.inviteToOrganization(email, organizationId, role);
  }

  async acceptInvitation(invitationId: string): Promise<AuthResponse> {
    return this.membership.acceptInvitation(invitationId);
  }

  async rejectInvitation(invitationId: string): Promise<AuthResponse> {
    return this.membership.rejectInvitation(invitationId);
  }

  async leaveOrganization(organizationId: string): Promise<AuthResponse> {
    return this.membership.leaveOrganization(organizationId);
  }

  async removeMember(memberIdOrEmail: string, organizationId: string): Promise<AuthResponse> {
    return this.membership.removeMember(memberIdOrEmail, organizationId);
  }

  async updateMemberRole(memberId: string, organizationId: string, role: 'admin' | 'member' | 'owner'): Promise<AuthResponse> {
    return this.membership.updateMemberRole(memberId, organizationId, role);
  }

  // Admin Methods
  async updateUserRole(userId: string, role: string, organizationId?: string): Promise<AuthResponse> {
    return this.admin.updateUserRole(userId, role, organizationId);
  }

  async getAllUsers(): Promise<User[]> {
    return this.admin.getAllUsers();
  }

  async getSystemStats(): Promise<any> {
    return this.admin.getSystemStats();
  }

  async isAdmin(): Promise<boolean> {
    return this.admin.isAdmin();
  }

  async isSuperAdmin(): Promise<boolean> {
    return this.admin.isSuperAdmin();
  }

  // Utility Methods for accessing individual services
  get coreAuthService() {
    return this.coreAuth;
  }

  get userManagementService() {
    return this.userManagement;
  }

  get organizationService() {
    return this.organization;
  }

  get membershipService() {
    return this.membership;
  }

  get adminService() {
    return this.admin;
  }
}