import type { AuthResponse, Organization } from '../../types/auth.types';
import { authClient } from '../../lib/auth-client';
import { CoreAuthService } from './auth.service';

/**
 * Organization Management service
 * Handles organization CRUD operations and organization-related functionality
 */
export class OrganizationService {
  private coreAuthService: CoreAuthService;

  constructor() {
    this.coreAuthService = new CoreAuthService();
  }

  /**
   * Create a new organization
   * @param name - Organization name
   * @param slug - Organization slug
   * @param logo - Organization logo URL (optional)
   * @returns Promise<AuthResponse>
   */
  async createOrganization(name: string, slug: string, logo?: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Creating organization:', { name, slug });

      // Check if user is superadmin and use custom endpoint
      const currentUser = await this.coreAuthService.checkAuthState();
      if (currentUser?.role === 'superadmin') {
        const response = await fetch('/api/admin/create-organization', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ name, slug, logo }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('❌ Create organization error:', errorData.error);
          return { success: false, error: errorData.error || 'Failed to create organization' };
        }

        const data = await response.json();
        console.log('✅ Organization created successfully (superadmin):', data.organization);
        return { success: true };
      }

      // For non-superadmin users, use the default Better Auth method
      const result = await authClient.organization.create({
        name,
        slug,
        logo,
      });

      if (result.error) {
        console.error('❌ Create organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to create organization' };
      }

      console.log('✅ Organization created successfully:', result.data);
      return { success: true };
    } catch (error) {
      console.error('❌ Create organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get list of user's organizations
   * @returns Promise<Organization[]>
   */
  async listOrganizations(): Promise<Organization[]> {
    try {
      const result = await authClient.organization.list();
      
      if (result.error) {
        console.error('❌ List organizations error:', result.error);
        return [];
      }

      return result.data || [];
    } catch (error) {
      console.error('❌ List organizations exception:', error);
      return [];
    }
  }

  /**
   * Set active organization
   * @param organizationId - Organization ID to set as active
   * @returns Promise<AuthResponse>
   */
  async setActiveOrganization(organizationId: string): Promise<AuthResponse> {
    try {
      const result = await authClient.organization.setActive({
        organizationId,
      });

      if (result.error) {
        console.error('❌ Set active organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to set active organization' };
      }

      return { success: true };
    } catch (error) {
      console.error('❌ Set active organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Get user's organization memberships
   * @param userId - User ID
   * @returns Promise<Organization[]>
   */
  async getUserOrganizations(userId: string): Promise<Organization[]> {
    try {
      // Use custom endpoint to get user organizations
      const response = await fetch(`/api/organization/user-organizations/${userId}`, {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        console.error('❌ Get user organizations error:', response.statusText);
        return [];
      }

      const data = await response.json();
      return data.organizations || [];
    } catch (error) {
      console.error('❌ Get user organizations exception:', error);
      return [];
    }
  }

  /**
   * Get organization by slug
   * @param organizationSlug - Organization slug
   * @returns Promise<Organization | null>
   */
  async getOrganizationBySlug(organizationSlug: string): Promise<Organization | null> {
    try {
      console.log('🔍 Getting organization by slug:', organizationSlug);
      console.log('🔍 Raw slug:', JSON.stringify(organizationSlug));
      
      const cleanedSlug = organizationSlug?.trim().toLowerCase();
      console.log('🔍 Cleaned slug:', JSON.stringify(cleanedSlug));
      
      const response = await fetch(`/api/admin/organization-by-slug/${cleanedSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`Failed to get organization: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('✅ API Result:', result);
      
      if (result.success && result.data) {
        console.log('✅ Organization found:', result.data);
        return result.data;
      } else {
        console.log('❌ Organization not found in result');
        throw new Error('Organization not found');
      }
    } catch (error) {
      console.error('❌ Error in getOrganizationBySlug:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  /**
   * Get all organizations (superadmin only)
   * @returns Promise<Organization[]>
   */
  async getAllOrganizations(): Promise<Organization[]> {
    try {
      console.log('🔍 Getting all organizations...');
      
      // Check if user is superadmin and use custom endpoint
      const currentUser = await this.coreAuthService.checkAuthState();
      if (currentUser?.role === 'superadmin') {
        const response = await fetch('/api/admin/all-organizations', {
          method: 'GET',
          credentials: 'include',
        });

        if (!response.ok) {
          console.error('❌ Get all organizations error:', response.statusText);
          return [];
        }

        const data = await response.json();
        console.log('✅ All organizations retrieved successfully (superadmin):', data.organizations?.length || 0);
        return data.organizations || [];
      }
      
      // For non-superadmin users, use the default Better Auth method
      const result = await authClient.organization.list();
      
      if (result.error) {
        console.error('❌ Get all organizations error:', result.error);
        return [];
      }

      console.log('✅ Organizations retrieved successfully:', result.data?.length || 0);
      return result.data || [];
    } catch (error) {
      console.error('❌ Get all organizations exception:', error);
      return [];
    }
  }

  /**
   * Update organization
   * @param organizationId - Organization ID
   * @param name - New organization name
   * @param slug - New organization slug
   * @returns Promise<AuthResponse>
   */
  async updateOrganization(organizationId: string, name: string, slug: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Updating organization:', { organizationId, name, slug });

      // Check if user is superadmin and use custom endpoint
      const currentUser = await this.coreAuthService.checkAuthState();
      if (currentUser?.role === 'superadmin') {
        const response = await fetch(`/api/admin/organizations/${organizationId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ name, slug }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('❌ Update organization error:', errorData.error);
          return { success: false, error: errorData.error || 'Failed to update organization' };
        }

        const data = await response.json();
        console.log('✅ Organization updated successfully (superadmin):', data.organization);
        return { success: true };
      }

      // For non-superadmin users, use the default Better Auth method
      const result = await authClient.organization.update({
        organizationId,
        data: {
          name,
          slug,
        },
      });

      if (result.error) {
        console.error('❌ Update organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to update organization' };
      }

      console.log('✅ Organization updated successfully:', result.data);
      return { success: true };
    } catch (error) {
      console.error('❌ Update organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Delete organization
   * @param organizationId - Organization ID to delete
   * @returns Promise<AuthResponse>
   */
  async deleteOrganization(organizationId: string): Promise<AuthResponse> {
    try {
      console.log('🔍 Deleting organization:', { organizationId });

      // Check if user is superadmin and use custom endpoint
      const currentUser = await this.coreAuthService.checkAuthState();
      if (currentUser?.role === 'superadmin') {
        const response = await fetch(`/api/admin/organizations/${organizationId}`, {
          method: 'DELETE',
          credentials: 'include',
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('❌ Delete organization error:', errorData.error);
          return { success: false, error: errorData.error || 'Failed to delete organization' };
        }

        const data = await response.json();
        console.log('✅ Organization deleted successfully (superadmin):', data.message);
        return { success: true };
      }

      // For non-superadmin users, use the default Better Auth method
      const result = await authClient.organization.delete({
        organizationId,
      });

      if (result.error) {
        console.error('❌ Delete organization error:', result.error);
        return { success: false, error: result.error.message || 'Failed to delete organization' };
      }

      console.log('✅ Organization deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Delete organization exception:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
}