import { ModuleType } from '../lib/constants/moduleTypes';

const API_BASE_URL = '/api';

export interface CustomPermission {
  id: string;
  module: ModuleType;
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export interface CustomRole {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  permissions: CustomPermission[];
  members?: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
  createdAt: string;
}

export interface CreateCustomRoleData {
  name: string;
  description?: string;
  organizationId: string;
  permissions: {
    moduleType: ModuleType;
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
  }[];
}

export interface UpdateCustomRoleData {
  name: string;
  description?: string;
  permissions: {
    moduleType: ModuleType;
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
  }[];
}

export interface UserPermissions {
  global: {
    role: string;
    isSuperAdmin: boolean;
  };
  organizations: {
    [organizationId: string]: {
      organization: {
        id: string;
        name: string;
      };
      role: string;
      customRole?: {
        id: string;
        name: string;
        description?: string;
      };
      permissions: {
        [moduleType: string]: {
          canCreate: boolean;
          canRead: boolean;
          canUpdate: boolean;
          canDelete: boolean;
        };
      };
    };
  };
}

export interface PermissionCheckResult {
  hasAccess: boolean;
  reason: string;
}

class CustomRoleService {
  // Get custom roles for an organization
  async getCustomRoles(organizationId: string): Promise<CustomRole[]> {
    try {
      console.log('🔍 Fetching custom roles for organization:', organizationId);
      const response = await fetch(`${API_BASE_URL}/admin/custom-roles/${organizationId}`, {
        method: 'GET',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch custom roles');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Custom roles fetched successfully:', data.customRoles.length);
        return data.customRoles;
      } else {
        console.error('❌ Failed to fetch custom roles:', data.error);
        throw new Error(data.error || 'Failed to fetch custom roles');
      }
    } catch (error: any) {
      console.error('❌ Error fetching custom roles:', error);
      throw error;
    }
  }

  // Create custom role
  async createCustomRole(data: CreateCustomRoleData): Promise<CustomRole> {
    try {
      console.log('🔍 Creating custom role:', data);
      const response = await fetch(`${API_BASE_URL}/admin/custom-roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create custom role');
      }
      
      const responseData = await response.json();
      
      if (responseData.success) {
        console.log('✅ Custom role created successfully:', responseData.customRole);
        return responseData.customRole;
      } else {
        console.error('❌ Failed to create custom role:', responseData.error);
        throw new Error(responseData.error || 'Failed to create custom role');
      }
    } catch (error: any) {
      console.error('❌ Error creating custom role:', error);
      throw error;
    }
  }

  // Update custom role
  async updateCustomRole(roleId: string, data: UpdateCustomRoleData): Promise<CustomRole> {
    try {
      console.log('🔍 Updating custom role:', roleId, data);
      const response = await fetch(`${API_BASE_URL}/admin/custom-roles/${roleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update custom role');
      }
      
      const responseData = await response.json();
      
      if (responseData.success) {
        console.log('✅ Custom role updated successfully:', responseData.customRole);
        return responseData.customRole;
      } else {
        console.error('❌ Failed to update custom role:', responseData.error);
        throw new Error(responseData.error || 'Failed to update custom role');
      }
    } catch (error: any) {
      console.error('❌ Error updating custom role:', error);
      throw error;
    }
  }

  // Delete custom role
  async deleteCustomRole(roleId: string): Promise<void> {
    try {
      console.log('🔍 Deleting custom role:', roleId);
      const response = await fetch(`${API_BASE_URL}/admin/custom-roles/${roleId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete custom role');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Custom role deleted successfully');
      } else {
        console.error('❌ Failed to delete custom role:', data.error);
        throw new Error(data.error || 'Failed to delete custom role');
      }
    } catch (error: any) {
      console.error('❌ Error deleting custom role:', error);
      throw error;
    }
  }

  // Assign custom role to member
  async assignCustomRole(memberId: string, customRoleId: string): Promise<any> {
    try {
      console.log('🔍 Assigning custom role:', { memberId, customRoleId });
      const response = await fetch(`${API_BASE_URL}/admin/assign-custom-role`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          memberId,
          customRoleId
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to assign custom role');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Custom role assigned successfully:', data.member);
        return data.member;
      } else {
        console.error('❌ Failed to assign custom role:', data.error);
        throw new Error(data.error || 'Failed to assign custom role');
      }
    } catch (error: any) {
      console.error('❌ Error assigning custom role:', error);
      throw error;
    }
  }

  // Get user permissions
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    try {
      console.log('🔍 Fetching user permissions for:', userId);
      const response = await fetch(`${API_BASE_URL}/admin/user-permissions/${userId}`, {
        method: 'GET',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch user permissions');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ User permissions fetched successfully:', data.permissions);
        return data.permissions;
      } else {
        console.error('❌ Failed to fetch user permissions:', data.error);
        throw new Error(data.error || 'Failed to fetch user permissions');
      }
    } catch (error: any) {
      console.error('❌ Error fetching user permissions:', error);
      throw error;
    }
  }

  // Check specific permission
  async checkPermission(
    userId: string,
    organizationId: string,
    moduleType: ModuleType,
    action: 'create' | 'read' | 'update' | 'delete'
  ): Promise<PermissionCheckResult> {
    try {
      console.log('🔍 Checking permission:', { userId, organizationId, moduleType, action });
      const response = await fetch(`${API_BASE_URL}/admin/check-permission`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId,
          organizationId,
          moduleType,
          action
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to check permission');
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Permission check result:', data);
        return {
          hasAccess: data.hasAccess,
          reason: data.reason
        };
      } else {
        console.error('❌ Failed to check permission:', data.error);
        throw new Error(data.error || 'Failed to check permission');
      }
    } catch (error: any) {
      console.error('❌ Error checking permission:', error);
      throw error;
    }
  }
}

export const customRoleService = new CustomRoleService();
export default customRoleService;