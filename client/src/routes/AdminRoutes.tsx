import { Route, Navigate } from 'react-router-dom';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import {
  AdminOrganizationWrapper,
  AdminDashboard,
  AdminManajemenPengunjung,
  AdminManajemenKonten,
  AdminManajemenPengguna,
  AdminBackupRestore,
  AdminLogs
} from '../pages/admin/[slug]';

/**
 * Admin routes configuration with organization-based dynamic routing
 * Returns individual Route components for admin functionality
 */
export function getAdminRoutes() {
  return [
    // Default admin redirect - will be handled by AdminOrganizationWrapper
    <Route key="admin-root" path="/admin" element={
      <ProtectedRoute requiredRole="admin">
        <Navigate to="/admin/dashboard" replace />
      </ProtectedRoute>
    } />,
    
    // Organization-based dynamic routes
    <Route key="admin-dashboard" path="/admin/:slug/dashboard" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminDashboard />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-pengunjung" path="/admin/:slug/manajemen-pengunjung" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminManajemenPengunjung />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-konten" path="/admin/:slug/manajemen-konten" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminManajemenKonten />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-pengguna" path="/admin/:slug/manajemen-pengguna" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminManajemenPengguna />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-backup" path="/admin/:slug/backup-restore" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminBackupRestore />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-logs" path="/admin/:slug/logs" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <AdminLogs />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    // Legacy routes without slug - redirect to organization-specific routes
    <Route key="admin-dashboard-legacy" path="/admin/dashboard" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminDashboard />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-pengunjung-legacy" path="/admin/manajemen-pengunjung" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminManajemenPengunjung />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-konten-legacy" path="/admin/manajemen-konten" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminManajemenKonten />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-pengguna-legacy" path="/admin/manajemen-pengguna" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminManajemenPengguna />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-backup-legacy" path="/admin/backup-restore" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminBackupRestore />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    <Route key="admin-logs-legacy" path="/admin/logs" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper redirectToSlug={true}>
          <AdminLogs />
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />,
    
    // Catch-all for any other admin paths
    <Route key="admin-catchall" path="/admin/*" element={
      <ProtectedRoute requiredRole="admin">
        <AdminOrganizationWrapper>
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900">Halaman Tidak Ditemukan</h2>
            <p className="text-gray-600 mt-2">Halaman yang Anda cari tidak tersedia</p>
            <button 
              onClick={() => window.location.href = '/admin/dashboard'}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Kembali ke Dashboard
            </button>
          </div>
        </AdminOrganizationWrapper>
      </ProtectedRoute>
    } />
  ];
}