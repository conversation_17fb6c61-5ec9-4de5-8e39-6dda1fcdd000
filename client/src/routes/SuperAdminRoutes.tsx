import { Route } from 'react-router-dom';
import {
  Dashboard,
  ManajemenPengunjung,
  ManajemenPengguna,
  ManajemenKonten,
  RoleManagement,
  ManajemenOrganisasi,
  BackupRestore,
  Logs
} from '../pages/superadmin';
import { SuperAdminLayout } from '../pages/superadmin/SuperAdminLayout';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { Navigate } from 'react-router-dom';

/**
 * SuperAdmin routes configuration
 * Returns individual Route components for superadmin functionality
 */
export function getSuperAdminRoutes() {
  return [
    // Default superadmin redirect
    <Route key="superadmin-root" path="/superadmin" element={
      <ProtectedRoute requiredRole="superadmin">
        <Navigate to="/superadmin/dashboard" replace />
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-dashboard" path="/superadmin/dashboard" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <Dashboard />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-pengunjung" path="/superadmin/manajemen-pengunjung" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <ManajemenPengunjung />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-sistem" path="/superadmin/pengaturan-sistem" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900">Pengaturan Sistem</h2>
            <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
          </div>
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-pengguna" path="/superadmin/manajemen-pengguna" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <ManajemenPengguna />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-konten" path="/superadmin/manajemen-konten" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <ManajemenKonten />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-roles" path="/superadmin/role-management" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <RoleManagement />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-organisasi" path="/superadmin/manajemen-organisasi" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <ManajemenOrganisasi />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-backup" path="/superadmin/backup-restore" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <BackupRestore />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    <Route key="superadmin-logs" path="/superadmin/logs" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <Logs />
        </SuperAdminLayout>
      </ProtectedRoute>
    } />,
    
    // Catch-all for any other superadmin paths
    <Route key="superadmin-catchall" path="/superadmin/*" element={
      <ProtectedRoute requiredRole="superadmin">
        <SuperAdminLayout>
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold text-gray-900">Halaman Tidak Ditemukan</h2>
            <p className="text-gray-600 mt-2">Halaman yang Anda cari tidak tersedia</p>
            <button 
              onClick={() => window.location.href = '/superadmin/dashboard'}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Kembali ke Dashboard
            </button>
          </div>
        </SuperAdminLayout>
      </ProtectedRoute>
    } />
  ];
}