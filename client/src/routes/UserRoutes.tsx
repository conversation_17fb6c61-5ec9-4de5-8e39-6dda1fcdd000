import { Route, Navigate } from 'react-router-dom';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { VisitorQuizRoute } from '../components/routes/VisitorQuizRoute';
import { UserLogin } from '../pages/auth/userAuth/UserLogin';
import { UserRegister } from '../pages/auth/userAuth/UserRegister';
import { UserForgotPassword } from '../pages/auth/userAuth/UserForgotPassword';

/**
 * User routes configuration
 * Returns individual Route components for user functionality
 * Users can only access /user/* paths and /user/auth/* paths
 */
export function getUserRoutes() {
  return [
    // User authentication routes (accessible without login)
    <Route key="user-login" path="/user/login" element={<UserLogin />} />,
    <Route key="user-register" path="/user/register" element={<UserRegister />} />,
    <Route key="user-forgot-password" path="/user/forgot-password" element={<UserForgotPassword />} />,
    
    // User quiz route (accessible to all, but with role-based redirects)
    <Route key="user-quiz" path="/user/quiz" element={<VisitorQuizRoute />} />,
    

    
    // Default user redirect - direct redirect without protection since /user/quiz handles role-based redirects
    <Route key="user-root" path="/user" element={
      <Navigate to="/user/quiz" replace />
    } />,
    
    // Catch-all for any other user paths - only for authenticated users with 'user' role
    <Route key="user-catchall" path="/user/*" element={
      <ProtectedRoute requiredRole="user">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900">Halaman Tidak Ditemukan</h2>
          <p className="text-gray-600 mt-2">Halaman yang Anda cari tidak tersedia</p>
          <button 
            onClick={() => window.location.href = '/user/quiz'}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Kembali ke Quiz
          </button>
        </div>
      </ProtectedRoute>
    } />
  ];
}