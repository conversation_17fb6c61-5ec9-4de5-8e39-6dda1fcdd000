/**
 * Authentication-related type definitions
 */

/**
 * User interface definition
 */
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin' | 'superadmin';
  emailVerified: boolean;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Authentication context interface
 */
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signup: (email: string, password: string, name: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  checkPermission: (resource: string, action: string) => boolean;
}

/**
 * Authentication response types
 */
export interface AuthResponse {
  success: boolean;
  error?: string;
}

export interface SessionData {
  user: User;
}

/**
 * Role-based permissions mapping
 */
export type RolePermissions = {
  [key in User['role']]: string[];
};

/**
 * Organization interface definition
 */
export interface Organization {
  id: string;
  name: string;
  slug?: string | null;
  logo?: string | null;
  createdAt: string | Date;
  metadata?: any;
}

/**
 * Organization member interface
 */
export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: 'admin' | 'member' | 'owner';
  createdAt: string;
  user: User;
}

/**
 * Organization member with detailed info interface
 */
export interface OrganizationMemberDetail {
  id: string; // User ID (sekarang menggunakan user ID sebagai identifier utama)
  name: string;
  email: string;
  role: string; // Organization role
  systemRole: 'user' | 'admin' | 'superadmin'; // System role
  memberId: string; // Member ID untuk referensi
  joinedAt: string;
  lastActive: string;
}

/**
 * Organization invitation interface
 */
export interface OrganizationInvitation {
  id: string;
  organizationId: string;
  email: string;
  role?: 'admin' | 'member' | 'owner';
  status: 'pending' | 'accepted' | 'rejected' | 'cancelled';
  expiresAt: string;
  inviterId: string;
  organization: Organization;
  inviter: User;
}

/**
 * Organization role types
 */
export type OrganizationRole = 'admin' | 'member' | 'owner';