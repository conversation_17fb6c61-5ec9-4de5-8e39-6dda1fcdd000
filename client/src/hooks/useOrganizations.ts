import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services/auth.service';
import { Organization } from '../types/auth.types';

interface UseOrganizationsReturn {
  organizations: Organization[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  createOrganization: (data: { name: string; slug: string }) => Promise<boolean>;
  updateOrganization: (id: string, data: { name: string; slug: string }) => Promise<boolean>;
  deleteOrganization: (id: string) => Promise<boolean>;
  modalLoading: boolean;
  modalError: string | null;
}

export function useOrganizations(): UseOrganizationsReturn {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [modalError, setModalError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter organizations based on search term
  // const filteredOrganizations = organizations.filter(org =>
  //   org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
  //   (org.slug && org.slug.toLowerCase().includes(searchTerm.toLowerCase()))
  // );

  // Load organizations from API
  const loadOrganizations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await authService.getAllOrganizations();
      setOrganizations(data);
    } catch (err) {
      console.error('Error loading organizations:', err);
      setError('Gagal memuat data organisasi');
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new organization
  const createOrganization = useCallback(async (data: { name: string; slug: string }): Promise<boolean> => {
    try {
      setModalLoading(true);
      setModalError(null);
      
      const result = await authService.createOrganization(data.name, data.slug);
      if (result.success) {
        await loadOrganizations(); // Reload the list
        return true;
      } else {
        throw new Error(result.error || 'Failed to create organization');
      }
    } catch (err: any) {
      console.error('Error creating organization:', err);
      setModalError(err.message || 'Gagal membuat organisasi');
      return false;
    } finally {
      setModalLoading(false);
    }
  }, [loadOrganizations]);

  // Update existing organization
  const updateOrganization = useCallback(async (id: string, data: { name: string; slug: string }): Promise<boolean> => {
    try {
      setModalLoading(true);
      setModalError(null);
      
      const result = await authService.updateOrganization(id, data.name, data.slug);
      if (result.success) {
        await loadOrganizations(); // Reload the list
        return true;
      } else {
        throw new Error(result.error || 'Failed to update organization');
      }
    } catch (err: any) {
      console.error('Error updating organization:', err);
      setModalError(err.message || 'Gagal memperbarui organisasi');
      return false;
    } finally {
      setModalLoading(false);
    }
  }, [loadOrganizations]);

  // Delete organization
  const deleteOrganization = useCallback(async (id: string): Promise<boolean> => {
    try {
      setModalLoading(true);
      setModalError(null);
      
      const result = await authService.deleteOrganization(id);
      if (result.success) {
        await loadOrganizations(); // Reload the list
        return true;
      } else {
        throw new Error(result.error || 'Failed to delete organization');
      }
    } catch (err: any) {
      console.error('Error deleting organization:', err);
      setModalError(err.message || 'Gagal menghapus organisasi');
      return false;
    } finally {
      setModalLoading(false);
    }
  }, [loadOrganizations]);

  // Clear form error
  // const clearFormError = useCallback(() => {
  //   setFormError('');
  // }, []);

  // Load organizations on mount
  useEffect(() => {
    loadOrganizations();
  }, [loadOrganizations]);

  return {
    organizations,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    createOrganization,
    updateOrganization,
    deleteOrganization,
    modalLoading,
    modalError
  };
}