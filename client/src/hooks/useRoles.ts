import { useState, useEffect, useCallback } from 'react';
import { 
  roleManagementService, 
  type RoleWithDetails,
  type UniqueRole, 
  type CreateRoleData, 
  type UpdateRoleData,
  type CreateCustomRoleData,
  type CustomRoleWithPermissions,
  type UpdateCustomRoleData
} from '../services/auth/role-management.service';
import type { AuthResponse } from '../types/auth.types';

/**
 * Hook for managing roles (Member model with User and Organization relations)
 */
export const useRoles = () => {
  const [roles, setRoles] = useState<RoleWithDetails[]>([]);
  const [uniqueRoles, setUniqueRoles] = useState<UniqueRole[]>([]);
  const [customRoles, setCustomRoles] = useState<CustomRoleWithPermissions[]>([]);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [availableOrganizations, setAvailableOrganizations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [useUniqueView, setUseUniqueView] = useState(true);

  // Calculate pagination
  const currentData = useUniqueView ? uniqueRoles : roles;
  const totalItems = currentData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRoles = currentData.slice(startIndex, endIndex);

  /**
   * Load roles from server
   */
  const loadRoles = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const rolesData = await roleManagementService.listRoles(searchValue);
      setRoles(rolesData);
    } catch (err) {
      setError('Failed to load roles');
      console.error('Error loading roles:', err);
    } finally {
      setLoading(false);
    }
  }, [searchValue]);

  /**
   * Load unique roles from server
   */
  const loadUniqueRoles = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const uniqueRolesData = await roleManagementService.listUniqueRoles(
        searchValue,
        1,
        1000 // Get all for client-side pagination
      );
      setUniqueRoles(uniqueRolesData);
    } catch (err) {
      setError('Failed to load unique roles');
      console.error('Error loading unique roles:', err);
    } finally {
      setLoading(false);
    }
  }, [searchValue]);

  /**
   * Load available users for role assignment
   */
  const loadAvailableUsers = useCallback(async () => {
    try {
      const users = await roleManagementService.getAvailableUsers();
      setAvailableUsers(users);
    } catch (err) {
      console.error('Error loading available users:', err);
    }
  }, []);

  /**
   * Load available organizations for role assignment
   */
  const loadAvailableOrganizations = useCallback(async () => {
    try {
      const organizations = await roleManagementService.getAvailableOrganizations();
      setAvailableOrganizations(organizations);
    } catch (err) {
      console.error('Error loading available organizations:', err);
    }
  }, []);

  /**
   * Load custom roles from server
   */
  const loadCustomRoles = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await roleManagementService.listCustomRoles(searchValue, 1, 1000);
      setCustomRoles(result.customRoles);
    } catch (err) {
      setError('Failed to load custom roles');
      console.error('Error loading custom roles:', err);
    } finally {
      setLoading(false);
    }
  }, [searchValue]);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    if (useUniqueView) {
      await Promise.all([
        loadUniqueRoles(),
        loadCustomRoles(),
        loadAvailableUsers(),
        loadAvailableOrganizations()
      ]);
    } else {
      await Promise.all([
        loadRoles(),
        loadCustomRoles(),
        loadAvailableUsers(),
        loadAvailableOrganizations()
      ]);
    }
  }, [useUniqueView, loadRoles, loadUniqueRoles, loadCustomRoles, loadAvailableUsers, loadAvailableOrganizations]);

  /**
   * Create a new role assignment
   */
  const createRole = useCallback(async (roleData: CreateRoleData): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.createRole(roleData);
      
      if (response.success) {
        await loadRoles(); // Refresh the list
        await loadAvailableUsers(); // Refresh available users
      } else {
        setError(response.error || 'Failed to create role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to create role';
      setError(errorMessage);
      console.error('Error creating role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [loadRoles, loadAvailableUsers]);

  /**
   * Update an existing role
   */
  const updateRole = useCallback(async (roleId: string, roleData: UpdateRoleData): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.updateRole(roleId, roleData);
      
      if (response.success) {
        await loadRoles(); // Refresh the list
      } else {
        setError(response.error || 'Failed to update role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to update role';
      setError(errorMessage);
      console.error('Error updating role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [loadRoles]);



  /**
   * Delete a role assignment
   */
  const deleteRole = useCallback(async (roleId: string): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.deleteRole(roleId);
      
      if (response.success) {
        await refreshData(); // Refresh all data
      } else {
        setError(response.error || 'Failed to delete role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to delete role';
      setError(errorMessage);
      console.error('Error deleting role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [refreshData]);

  /**
   * Create a custom role with permissions
   */
  const createCustomRole = useCallback(async (customRoleData: CreateCustomRoleData): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.createCustomRole(customRoleData);
      
      if (response.success) {
        await refreshData(); // Refresh all data
      } else {
        setError(response.error || 'Failed to create custom role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to create custom role';
      setError(errorMessage);
      console.error('Error creating custom role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [refreshData]);

  /**
   * Update a custom role
   */
  const updateCustomRole = useCallback(async (customRoleId: string, customRoleData: UpdateCustomRoleData): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.updateCustomRole(customRoleId, customRoleData);
      
      if (response.success) {
        await refreshData(); // Refresh all data
      } else {
        setError(response.error || 'Failed to update custom role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to update custom role';
      setError(errorMessage);
      console.error('Error updating custom role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [refreshData]);

  /**
   * Delete a custom role
   */
  const deleteCustomRole = useCallback(async (customRoleId: string): Promise<AuthResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await roleManagementService.deleteCustomRole(customRoleId);
      
      if (response.success) {
        await refreshData(); // Refresh all data
      } else {
        setError(response.error || 'Failed to delete custom role');
      }
      
      return response;
    } catch (err) {
      const errorMessage = 'Failed to delete custom role';
      setError(errorMessage);
      console.error('Error deleting custom role:', err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [refreshData]);

  /**
   * Get a custom role by ID
   */
  const getCustomRole = useCallback(async (customRoleId: string): Promise<CustomRoleWithPermissions | null> => {
    try {
      return await roleManagementService.getCustomRole(customRoleId);
    } catch (err) {
      console.error('Error getting custom role:', err);
      return null;
    }
  }, []);

  /**
   * Handle search
   */
  const handleSearch = useCallback((value: string) => {
    setSearchValue(value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load initial data
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Reload roles when search value changes
  useEffect(() => {
    if (useUniqueView) {
      loadUniqueRoles();
    } else {
      loadRoles();
    }
    loadCustomRoles();
  }, [useUniqueView, loadRoles, loadUniqueRoles, loadCustomRoles]);

  return {
    // Data
    roles: currentRoles,
    allRoles: roles,
    uniqueRoles,
    customRoles,
    availableUsers,
    availableOrganizations,
    
    // State
    loading,
    error,
    searchValue,
    useUniqueView,
    
    // Pagination
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    setCurrentPage,
    
    // Actions
    createRole,
    updateRole,
    deleteRole,
    createCustomRole,
    updateCustomRole,
    deleteCustomRole,
    getCustomRole,
    handleSearch,
    clearError,
    refreshData,
    loadRoles,
    loadUniqueRoles,
    loadCustomRoles,
    loadAvailableUsers,
    loadAvailableOrganizations,
    setUseUniqueView
  };
};