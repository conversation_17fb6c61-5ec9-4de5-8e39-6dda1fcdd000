import { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { authService } from '../services/auth.service';
import { Organization } from '../types/auth.types';

interface UseAdminOrganizationReturn {
  organization: Organization | null;
  loading: boolean;
  error: string | null;
  setActiveOrganization: (orgId: string) => Promise<void>;
}

/**
 * Hook untuk menangani organisasi context untuk admin
 * Mengambil organisasi berdasarkan slug dari URL dan memastikan admin memiliki akses
 */
export function useAdminOrganization(redirectToSlug: boolean = false): UseAdminOrganizationReturn {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAdminOrganization = async () => {
      try {
        setLoading(true);
        setError(null);

        // Cek apakah user adalah admin
        const currentUser = await authService.checkAuthState();
        if (!currentUser || currentUser.role !== 'admin') {
          setError('Unauthorized: Admin access required');
          navigate('/login');
          return;
        }

        // Ambil organisasi user
        const userOrganizations = await authService.getUserOrganizations(currentUser.id);
        
        if (!userOrganizations || userOrganizations.length === 0) {
          setError('No organizations found for this admin');
          return;
        }

        if (!slug || redirectToSlug) {
          // Jika tidak ada slug atau perlu redirect, ambil organisasi pertama yang tersedia
          if (userOrganizations.length > 0) {
            const firstOrg = userOrganizations[0];
            setOrganization(firstOrg);
            
            // Set organisasi aktif
            await authService.setActiveOrganization(firstOrg.id);
            
            // Hanya redirect jika benar-benar diperlukan
            if (redirectToSlug) {
              // Untuk legacy routes, tambahkan slug setelah /admin
              const currentPath = location.pathname;
              const pathAfterAdmin = currentPath.replace('/admin', '');
              const newPath = `/admin/${firstOrg.slug}${pathAfterAdmin}`;
              navigate(newPath, { replace: true });
              return;
            }
            
            // Jika tidak ada slug tapi bukan legacy route, redirect ke dashboard
            if (!slug) {
              navigate(`/admin/${firstOrg.slug}/dashboard`, { replace: true });
              return;
            }
          } else {
            setError('Anda tidak memiliki akses ke organisasi manapun');
          }
          return;
        }

        // Cari organisasi berdasarkan slug
        const targetOrg = userOrganizations.find(org => org.slug === slug);
        
        if (!targetOrg) {
          // Jika slug tidak ditemukan, redirect ke organisasi pertama
          const firstOrg = userOrganizations[0];
          if (firstOrg.slug) {
            navigate(`/admin/${firstOrg.slug}/dashboard`, { replace: true });
            return;
          } else {
            setError('Organization slug not found');
            return;
          }
        }

        // Set organisasi aktif
        await authService.setActiveOrganization(targetOrg.id);
        setOrganization(targetOrg);
        
      } catch (err) {
        console.error('Error loading admin organization:', err);
        setError('Failed to load organization');
      } finally {
        setLoading(false);
      }
    };

    // Load organization bahkan tanpa slug untuk handle redirect
    loadAdminOrganization();
  }, [slug, redirectToSlug]); // Hapus location.pathname dan navigate dari dependency

  const setActiveOrganization = async (orgId: string) => {
    try {
      await authService.setActiveOrganization(orgId);
      // Refresh organization data
      const currentUser = await authService.checkAuthState();
      if (currentUser) {
        const userOrganizations = await authService.getUserOrganizations(currentUser.id);
        const updatedOrg = userOrganizations.find(org => org.id === orgId);
        if (updatedOrg) {
          setOrganization(updatedOrg);
        }
      }
    } catch (err) {
      console.error('Error setting active organization:', err);
      setError('Failed to set active organization');
    }
  };

  return {
    organization,
    loading,
    error,
    setActiveOrganization
  };
}