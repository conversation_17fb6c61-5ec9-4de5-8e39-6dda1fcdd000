import { useState, useEffect } from 'react';
import { authService } from '../../services/auth.service';
import { Organization } from '../../types/auth.types';

export interface UseOrganizationsReturn {
  organizations: Organization[];
  loading: boolean;
  error: string | null;
  loadOrganizations: () => Promise<void>;
}

export function useOrganizations(): UseOrganizationsReturn {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadOrganizations = async () => {
    try {
      setLoading(true);
      setError(null);
      const orgsData = await authService.getAllOrganizations();
      setOrganizations(orgsData);
    } catch (error) {
      console.error('Error loading organizations:', error);
      setError('Gagal memuat data organisasi');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrganizations();
  }, []);

  return {
    organizations,
    loading,
    error,
    loadOrganizations
  };
}