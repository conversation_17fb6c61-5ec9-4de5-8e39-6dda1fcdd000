import { useState, useEffect } from 'react';
import { authService } from '../../services/auth.service';
import { Organization } from '../../types/auth.types';

export interface UserWithOrganization {
  id: string;
  name: string;
  email: string;
  role: string;
  customRoleId?: string; // For custom roles
  organizations: Organization[];
  createdAt: string;
  updatedAt: string;
}

export interface UseUsersReturn {
  users: UserWithOrganization[];
  loading: boolean;
  error: string | null;
  loadUsers: () => Promise<void>;
  createUser: (userData: CreateUserData) => Promise<{ success: boolean; error?: string }>;
  updateUser: (userId: string, userData: UpdateUserData) => Promise<{ success: boolean; error?: string }>;
  deleteUser: (userId: string) => Promise<{ success: boolean; error?: string }>;
}

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: 'user' | 'admin' | 'superadmin' | 'custom';
  customRoleId?: string; // For custom roles
  organizationId?: string;
}

export interface UpdateUserData {
  name: string;
  email: string;
  password?: string;
  role: 'user' | 'admin' | 'superadmin' | 'custom';
  customRoleId?: string; // For custom roles
  organizationId?: string;
}

export function useUsers(
  searchQuery: string,
  pageSize: number,
  currentPage: number
): UseUsersReturn {
  const [users, setUsers] = useState<UserWithOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const usersData = await authService.listUsers(
        searchQuery || undefined,
        pageSize,
        currentPage * pageSize
      );

      // Transform users data and add organization info
      const usersWithOrganizations: UserWithOrganization[] = await Promise.all(
        usersData.map(async (user: any) => {
          try {
            const organizations = await authService.getUserOrganizations(user.id);
            return {
              id: user.id,
              name: user.name || 'N/A',
              email: user.email,
              role: user.role || 'user',
              customRoleId: user.customRoleId,
              organizations: organizations,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            };
          } catch (error) {
            console.error('Error loading organizations for user:', user.id, error);
            return {
              id: user.id,
              name: user.name || 'N/A',
              email: user.email,
              role: user.role || 'user',
              customRoleId: user.customRoleId,
              organizations: [],
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            };
          }
        })
      );

      setUsers(usersWithOrganizations);
    } catch (error) {
      console.error('Error loading users:', error);
      setError('Gagal memuat data pengguna');
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (userData: CreateUserData) => {
    try {
      const organizationId = userData.role !== 'superadmin' && userData.organizationId !== 'none' 
        ? userData.organizationId 
        : undefined;

      // Fix parameter order: email, password, name, role, organizationId, customRoleId
      const result = await authService.createUser(
        userData.email,
        userData.password,
        userData.name,
        userData.role,
        organizationId,
        userData.customRoleId
      );

      if (result.success) {
        await loadUsers();
      }

      return result;
    } catch (error) {
      console.error('Error creating user:', error);
      return { success: false, error: 'Terjadi kesalahan saat membuat pengguna' };
    }
  };

  const updateUser = async (userId: string, userData: UpdateUserData) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) {
        return { success: false, error: 'User tidak ditemukan' };
      }

      const updateData: any = {
        name: userData.name,
        email: userData.email
      };

      if (userData.role !== user.role) {
        updateData.role = userData.role;
        const currentOrgId = user.organizations[0]?.id;
        if (currentOrgId) {
          updateData.organizationId = currentOrgId;
        }
        
        // Add customRoleId for custom roles
        if (userData.role === 'custom' && userData.customRoleId) {
          updateData.customRoleId = userData.customRoleId;
        }
      }

      const result = await authService.updateUser(userId, updateData);

      if (userData.password) {
        const passwordResult = await authService.updateUserPassword(userId, userData.password);
        if (!passwordResult.success) {
          return { success: false, error: passwordResult.error || 'Failed to update password' };
        }
      }

      if (result.success) {
        const finalRole = updateData.role || user.role;
        
        if (finalRole !== 'superadmin') {
          const currentOrgId = user.organizations[0]?.id || null;
          const newOrgId = userData.organizationId === 'none' ? null : userData.organizationId;
          
          if (currentOrgId !== newOrgId) {
            try {
              if (currentOrgId && !newOrgId) {
                await authService.removeUserFromOrganization(userId, currentOrgId);
              } else if (!currentOrgId && newOrgId) {
                const orgRole = finalRole === 'user' ? 'member' : finalRole as 'admin' | 'member';
                await authService.addUserToOrganization(userId, newOrgId, orgRole);
              } else if (currentOrgId && newOrgId && currentOrgId !== newOrgId) {
                await authService.removeUserFromOrganization(userId, currentOrgId);
                const orgRole = finalRole === 'user' ? 'member' : finalRole as 'admin' | 'member';
                await authService.addUserToOrganization(userId, newOrgId, orgRole);
              }
            } catch (orgError) {
              console.warn('User updated but failed to update organization membership:', orgError);
            }
          }
        } else {
          if (updateData.role === 'superadmin') {
            const currentOrgId = user.organizations[0]?.id;
            if (currentOrgId) {
              try {
                await authService.removeUserFromOrganization(userId, currentOrgId);
              } catch (orgError) {
                console.warn('Failed to remove superadmin from organization:', orgError);
              }
            }
          }
        }
        
        await loadUsers();
      }

      return result;
    } catch (error) {
      console.error('Error updating user:', error);
      return { success: false, error: 'Terjadi kesalahan saat memperbarui pengguna' };
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      const result = await authService.deleteUser(userId);
      
      if (result.success) {
        await loadUsers();
      }
      
      return result;
    } catch (error) {
      console.error('Error deleting user:', error);
      return { success: false, error: 'Terjadi kesalahan saat menghapus pengguna' };
    }
  };

  useEffect(() => {
    loadUsers();
  }, [searchQuery, pageSize, currentPage]);

  return {
    users,
    loading,
    error,
    loadUsers,
    createUser,
    updateUser,
    deleteUser
  };
}