import { useState, useEffect } from 'react';

export interface UsePaginationReturn {
  searchQuery: string;
  pageSize: number;
  currentPage: number;
  setSearchQuery: (query: string) => void;
  setPageSize: (size: number) => void;
  setCurrentPage: (page: number) => void;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePageSizeChange: (value: string) => void;
  nextPage: () => void;
  prevPage: () => void;
}

export function usePagination(): UsePaginationReturn {
  const [searchQuery, setSearchQuery] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handlePageSizeChange = (value: string) => {
    setPageSize(parseInt(value));
    setCurrentPage(0);
  };

  const nextPage = () => {
    setCurrentPage(prev => prev + 1);
  };

  const prevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  // Reset to first page when searching
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(0);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  return {
    searchQuery,
    pageSize,
    currentPage,
    setSearchQuery,
    setPageSize,
    setCurrentPage,
    handleSearchChange,
    handlePageSizeChange,
    nextPage,
    prevPage
  };
}