import { useState } from 'react';
import { UserWithOrganization } from './useUsers';

export interface UserFormData {
  name: string;
  email: string;
  password: string;
  role: 'user' | 'admin' | 'superadmin' | 'custom';
  customRoleId?: string; // For custom roles
  organizationId: string;
}

export interface UseUserFormReturn {
  formData: UserFormData;
  formErrors: Record<string, string>;
  submitting: boolean;
  setFormData: (data: UserFormData) => void;
  setFormErrors: (errors: Record<string, string>) => void;
  setSubmitting: (submitting: boolean) => void;
  resetForm: () => void;
  validateForm: (isEdit?: boolean) => boolean;
  populateFormForEdit: (user: UserWithOrganization) => void;
}

const initialFormData: UserFormData = {
  name: '',
  email: '',
  password: '',
  role: 'user',
  customRoleId: undefined,
  organizationId: 'none'
};

export function useUserForm(): UseUserFormReturn {
  const [formData, setFormData] = useState<UserFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  const resetForm = () => {
    setFormData(initialFormData);
    setFormErrors({});
    setSubmitting(false);
  };

  const validateForm = (isEdit = false) => {
    const errors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Nama wajib diisi';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email wajib diisi';
    }
    
    if (!isEdit && !formData.password.trim()) {
      errors.password = 'Password wajib diisi';
    }
    
    if (formData.role === 'custom' && !formData.customRoleId) {
      errors.customRoleId = 'Custom role wajib dipilih';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const populateFormForEdit = (user: UserWithOrganization) => {
    setFormData({
      name: user.name,
      email: user.email,
      password: '',
      role: user.customRoleId ? 'custom' : (user.role as 'user' | 'admin' | 'superadmin'),
      customRoleId: user.customRoleId || undefined,
      organizationId: user.organizations[0]?.id || 'none'
    });
    setFormErrors({});
  };

  return {
    formData,
    formErrors,
    submitting,
    setFormData,
    setFormErrors,
    setSubmitting,
    resetForm,
    validateForm,
    populateFormForEdit
  };
}