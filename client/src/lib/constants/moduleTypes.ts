// ModuleType enum untuk permission system
export enum ModuleType {
  MANAJEMEN_PENGUNJUNG = 'manajemen_pengunjung',
  PENGATURAN_SISTEM = 'pengaturan_sistem',
  BACKUP_RESTORE = 'backup_restore',
  LOGS = 'logs',
  PROFIL = 'profil',
  DIGITAL_PERMIT = 'digital_permit'
}

// Mapping untuk display names
export const MODULE_DISPLAY_NAMES: Record<ModuleType, string> = {
  [ModuleType.MANAJEMEN_PENGUNJUNG]: 'Manajemen Pengunjung',
  [ModuleType.PENGATURAN_SISTEM]: 'Pengaturan Sistem',
  [ModuleType.BACKUP_RESTORE]: 'Backup & Restore',
  [ModuleType.LOGS]: 'Logs',
  [ModuleType.PROFIL]: 'Profil',
  [ModuleType.DIGITAL_PERMIT]: 'Digital Permit'
};

// Permission actions
export type PermissionAction = 'canRead' | 'canCreate' | 'canUpdate' | 'canDelete';

export const PERMISSION_ACTIONS: PermissionAction[] = ['canRead', 'canCreate', 'canUpdate', 'canDelete'];

export const PERMISSION_ACTION_LABELS: Record<PermissionAction, string> = {
  canRead: 'Read',
  canCreate: 'Create',
  canUpdate: 'Update',
  canDelete: 'Delete'
};