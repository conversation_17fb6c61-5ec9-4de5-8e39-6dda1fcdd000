import { ModuleType, PermissionAction } from '../constants/moduleTypes';

// Types untuk permission checking
export interface HasAccessParams {
  userId: string;
  orgId: string;
  module: ModuleType;
  action: PermissionAction;
}

export interface UserPermissions {
  userId: string;
  organizationId: string;
  role: string;
  customRole?: {
    id: string;
    name: string;
    permissions: {
      module: ModuleType;
      canRead: boolean;
      canCreate: boolean;
      canUpdate: boolean;
      canDelete: boolean;
    }[];
  };
}

// Default permissions untuk role standar
const DEFAULT_PERMISSIONS = {
  admin: {
    [ModuleType.MANAJEMEN_PENGUNJUNG]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true },
    [ModuleType.PENGATURAN_SISTEM]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true },
    [ModuleType.BACKUP_RESTORE]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true },
    [ModuleType.LOGS]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true },
    [ModuleType.PROFIL]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true },
    [ModuleType.DIGITAL_PERMIT]: { canRead: true, canCreate: true, canUpdate: true, canDelete: true }
  },
  member: {
    [ModuleType.MANAJEMEN_PENGUNJUNG]: { canRead: false, canCreate: false, canUpdate: false, canDelete: false },
    [ModuleType.PENGATURAN_SISTEM]: { canRead: false, canCreate: false, canUpdate: false, canDelete: false },
    [ModuleType.BACKUP_RESTORE]: { canRead: false, canCreate: false, canUpdate: false, canDelete: false },
    [ModuleType.LOGS]: { canRead: false, canCreate: false, canUpdate: false, canDelete: false },
    [ModuleType.PROFIL]: { canRead: true, canCreate: false, canUpdate: true, canDelete: false },
    [ModuleType.DIGITAL_PERMIT]: { canRead: true, canCreate: false, canUpdate: false, canDelete: false }
  }
};

/**
 * Function untuk mengecek apakah user memiliki akses ke module dan action tertentu
 */
export async function hasAccess(params: HasAccessParams): Promise<boolean> {
  const { userId, orgId, module, action } = params;
  
  try {
    // Fetch user permissions dari API
    const response = await fetch(`/api/admin/permissions/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ userId, organizationId: orgId, module, action })
    });
    
    if (!response.ok) {
      console.error('Failed to check permissions:', response.statusText);
      return false;
    }
    
    const result = await response.json();
    return result.hasAccess;
  } catch (error) {
    console.error('Error checking permissions:', error);
    return false;
  }
}

/**
 * Function untuk mengecek permission secara lokal (jika data sudah tersedia)
 */
export function hasAccessLocal(userPermissions: UserPermissions, module: ModuleType, action: PermissionAction): boolean {
  const { role, customRole } = userPermissions;
  
  // Jika role adalah custom, gunakan custom permissions
  if (role === 'custom' && customRole) {
    const modulePermission = customRole.permissions.find(p => p.module === module);
    if (modulePermission) {
      return modulePermission[action];
    }
    return false;
  }
  
  // Gunakan default permissions untuk role standar
  const rolePermissions = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS];
  if (rolePermissions && rolePermissions[module]) {
    return rolePermissions[module][action];
  }
  
  return false;
}

/**
 * Function untuk mendapatkan semua permissions user untuk organisasi tertentu
 */
export async function getUserPermissions(userId: string, organizationId: string): Promise<UserPermissions | null> {
  try {
    const response = await fetch(`/api/admin/permissions/user?userId=${userId}&organizationId=${organizationId}`, {
      credentials: 'include'
    });
    
    if (!response.ok) {
      console.error('Failed to get user permissions:', response.statusText);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting user permissions:', error);
    return null;
  }
}