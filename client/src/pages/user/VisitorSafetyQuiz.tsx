import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { VideoStep } from '../../components/quiz/VideoStep';
import { QuizStep } from '../../components/quiz/QuizStep';
import { QuizResult } from '../../components/quiz/QuizResult';
import { useQuiz } from '../../hooks/useQuiz';
import { signOut } from '../../lib/auth-client';

export function VisitorSafetyQuiz() {
  const navigate = useNavigate();
  const {
    currentStep,
    currentQuestion,
    selectedAnswer,
    score,
    questions,
    // quiz, // Not used directly in component
    video,
    loading,
    error,
    handleVideoComplete,
    handleAnswerSelect,
    handleNextQuestion,
    handleRetakeQuiz,
  } = useQuiz();

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/user/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if there's an error
      navigate('/user/login');
    }
  };

  // Loading Step
  if (currentStep === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading quiz data...</p>
        </div>
      </div>
    );
  }

  // Video Step
  if (currentStep === 'video') {
    return (
      <VideoStep
        video={video}
        loading={loading}
        error={error}
        onVideoComplete={handleVideoComplete}
        onLogout={handleLogout}
      />
    );
  }

  // Quiz Step
  if (currentStep === 'quiz') {
    const question = questions[currentQuestion];
    
    return (
      <QuizStep
        question={question}
        currentQuestion={currentQuestion}
        totalQuestions={questions.length}
        selectedAnswer={selectedAnswer}
        onAnswerSelect={handleAnswerSelect}
        onNextQuestion={handleNextQuestion}
        onLogout={handleLogout}
      />
    );
  }

  // Result Step
  if (currentStep === 'result') {
    return (
      <QuizResult
        score={score}
        onRetake={handleRetakeQuiz}
        onLogout={handleLogout}
      />
    );
  }

  // Error state
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <p className="text-red-600 mb-4">{error || 'Terjadi kesalahan yang tidak terduga'}</p>
        <Button onClick={() => window.location.reload()}>
          Refresh
        </Button>
      </div>
    </div>
  );
}
