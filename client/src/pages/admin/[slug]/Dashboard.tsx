import { useState, useEffect } from 'react';
import { StatsCards } from '../../../components/dashboard/StatsCards';
import { VisitorChart } from '../../../components/dashboard/VisitorChart';
import { HeatmapCard } from '../../../components/dashboard/HeatmapCard';
import { RecentVisitors } from '../../../components/dashboard/RecentVisitors';

// Types
export type DashboardStats = {
  totalPengunjungTerdaftar: number;
  totalPengunjungDisetujui: number;
  totalPengunjungDitolak: number;
  totalPengunjungKadaluwarsa: number;
};

export type ChartDataPoint = {
  day: string;
  value: number;
};

export type VisitorData = {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: 'Karyawan' | 'Umum';
  timestamp: string;
  status: 'Approved' | 'Need Approve' | 'Disapproved' | 'Expired';
};

// Using relative paths for single origin deployment

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [loading, setLoading] = useState(true);
  // Logic moved to individual components

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Mock data for stats
        setStats({
          totalPengunjungTerdaftar: 17,
          totalPengunjungDisetujui: 10,
          totalPengunjungDitolak: 2,
          totalPengunjungKadaluwarsa: 5,
        });

        // Mock data for chart
        const mockChartData: ChartDataPoint[] = [
          { day: 'Senin', value: 42 },
          { day: 'Selasa', value: 38 },
          { day: 'Rabu', value: 45 },
          { day: 'Kamis', value: 52 },
          { day: 'Jumat', value: 48 },
          { day: 'Sabtu', value: 35 },
          { day: 'Minggu', value: 28 }
        ];
        setChartData(mockChartData);

        // Mock data for visitors
        const mockVisitors: VisitorData[] = [
          {
            id: '1',
            name: 'Rina Pratama',
            email: '<EMAIL>',
            phone: '+628123456789',
            type: 'Karyawan',
            timestamp: '2025-06-26 09:15:23',
            status: 'Approved'
          },
          {
            id: '2',
            name: 'Budi Santoso',
            email: '<EMAIL>',
            phone: '+628987654321',
            type: 'Umum',
            timestamp: '2025-06-25 08:47:10',
            status: 'Need Approve'
          },
          {
            id: '3',
            name: 'Sari Wijaya',
            email: '<EMAIL>',
            phone: '+628564738291',
            type: 'Karyawan',
            timestamp: '2025-06-24 14:12:45',
            status: 'Disapproved'
          },
          {
            id: '4',
            name: 'Ahmad Fauzi',
            email: '<EMAIL>',
            phone: '+628773849291',
            type: 'Umum',
            timestamp: '2025-06-23 17:03:55',
            status: 'Expired'
          },
          {
            id: '5',
            name: 'Lestari Angraini',
            email: '<EMAIL>',
            phone: '+628221234567',
            type: 'Karyawan',
            timestamp: '2025-06-26 09:58:02',
            status: 'Approved'
          }
        ];
        setVisitors(mockVisitors);

        // Try to fetch real data if available
        try {
          const statsRes = await fetch(`/api/admin/dashboard/stats`, {
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (statsRes.ok) {
            const statsData = await statsRes.json();
            setStats(statsData);
          }

          const chartRes = await fetch(`/api/admin/dashboard/chart`, {
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (chartRes.ok) {
            const chartData = await chartRes.json();
            setChartData(chartData);
          }

          const visitorsRes = await fetch(`/api/admin/dashboard/visitors`, {
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (visitorsRes.ok) {
            const visitorsData = await visitorsRes.json();
            setVisitors(visitorsData);
          }
        } catch (error) {
          console.error('Error fetching dashboard data:', error);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Dashboard</h1>
      </div>

      {/* Stats Cards */}
      <StatsCards stats={stats} />

      {/* Chart and Heatmap */}
       <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
         <VisitorChart data={chartData} />
         <HeatmapCard />
       </div>

       {/* Recent Visitors Table */}
       <RecentVisitors visitors={visitors.map(visitor => ({
         id: visitor.id,
         name: visitor.name,
         company: visitor.email,
         type: visitor.type === 'Karyawan' ? 'PAM' : 'SMA',
         status: visitor.status === 'Approved' ? 'approved' : 
                visitor.status === 'Need Approve' ? 'pending' : 'rejected',
         date: visitor.timestamp
       }))} />
    </div>
  );
}