import { useState } from 'react';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Download, RotateCcw, Trash2, FileText, Upload } from 'lucide-react';

interface BackupItem {
  id: string;
  date: string;
  type: 'Auto-Backup' | 'Manual';
  status: 'Berhasil' | 'Gagal';
  size: string;
}

export function BackupRestore() {
  const [autoBackupSchedule, setAutoBackupSchedule] = useState<'daily' | 'weekly'>('daily');

  // Mock data for backup history
  const backupHistory: BackupItem[] = [
    {
      id: '1',
      date: '2023-10-01',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.5 GB'
    },
    {
      id: '2',
      date: '2023-09-24',
      type: 'Manual',
      status: 'Gagal',
      size: '1.2 GB'
    },
    {
      id: '3',
      date: '2023-09-15',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.3 GB'
    }
  ];

  const handleBackupNow = () => {
    // Implement backup functionality
    console.log('Starting backup...');
  };

  const handleSetSchedule = () => {
    // Implement schedule setting
    console.log('Setting schedule to:', autoBackupSchedule);
  };

  const handleDownload = (backupId: string) => {
    // Implement download functionality
    console.log('Downloading backup:', backupId);
  };

  const handleRestore = (backupId: string) => {
    // Implement restore functionality
    console.log('Restoring backup:', backupId);
  };

  const handleDelete = (backupId: string) => {
    // Implement delete functionality
    console.log('Deleting backup:', backupId);
  };

  const handleExportCSV = () => {
    console.log('Exporting as CSV...');
  };

  const handleExportJSON = () => {
    console.log('Exporting as JSON...');
  };

  const handleImportData = () => {
    console.log('Importing data...');
  };

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">Backup & Restore</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Jadwal Auto-Backup */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Jadwal Auto-Backup</h3>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="schedule"
                    value="daily"
                    checked={autoBackupSchedule === 'daily'}
                    onChange={(e) => setAutoBackupSchedule(e.target.value as 'daily')}
                    className="text-blue-600"
                  />
                  <span className="text-gray-700">Harian</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="schedule"
                    value="weekly"
                    checked={autoBackupSchedule === 'weekly'}
                    onChange={(e) => setAutoBackupSchedule(e.target.value as 'weekly')}
                    className="text-blue-600"
                  />
                  <span className="text-gray-700">Mingguan</span>
                </label>
              </div>

              <div className="flex gap-3 pt-4">
                <Button 
                  onClick={handleBackupNow}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Backup Sekarang
                </Button>
                <Button 
                  onClick={handleSetSchedule}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Atur Jadwal
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export/Import Data */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Export/Import Data</h3>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportCSV}
              >
                <FileText className="h-4 w-4 mr-2" />
                Export as CSV
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportJSON}
              >
                <FileText className="h-4 w-4 mr-2" />
                Export as JSON
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleImportData}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import CSV/JSON
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Log Riwayat Backup */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Log Riwayat Backup</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left p-4 font-medium text-gray-900">Tanggal</th>
                  <th className="text-left p-4 font-medium text-gray-900">Tipe</th>
                  <th className="text-left p-4 font-medium text-gray-900">Status</th>
                  <th className="text-left p-4 font-medium text-gray-900">Ukuran</th>
                  <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {backupHistory.map((backup) => (
                  <tr key={backup.id} className="border-b hover:bg-gray-50">
                    <td className="p-4 text-gray-900">{backup.date}</td>
                    <td className="p-4 text-gray-600">{backup.type}</td>
                    <td className="p-4">
                      <Badge className={backup.status === 'Berhasil'
                        ? 'bg-green-100 text-green-800 hover:bg-green-100'
                        : 'bg-red-100 text-red-800 hover:bg-red-100'
                      }>
                        {backup.status}
                      </Badge>
                    </td>
                    <td className="p-4 text-gray-600">{backup.size}</td>
                    <td className="p-4">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200"
                          onClick={() => handleDownload(backup.id)}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
                          onClick={() => handleRestore(backup.id)}
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Restore
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                          onClick={() => handleDelete(backup.id)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Indikator Penyimpanan */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Indikator Penyimpanan</h3>
          
          <div className="flex items-center space-x-6">
            {/* Pie Chart Placeholder */}
            <div className="relative w-32 h-32">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                {/* Background circle */}
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#e5e7eb"
                  strokeWidth="3"
                />
                {/* Used storage (35%) */}
                <path
                  d="M18 2.0845
                    a 15.9155 15.9155 0 0 1 0 31.831
                    a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="3"
                  strokeDasharray="35, 65"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-sm font-medium text-gray-900">35%</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Sisa Penyimpanan: 35 GB</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                <span className="text-sm text-gray-700">Digunakan: 65 GB</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
