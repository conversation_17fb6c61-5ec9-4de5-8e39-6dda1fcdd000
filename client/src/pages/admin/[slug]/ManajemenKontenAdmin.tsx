import { useState, useEffect } from 'react';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Upload, Plus, Edit, Trash2 } from 'lucide-react';
import { Input } from '../../../components/ui/input';
import { useAdminOrganization } from '../../../hooks/useAdminOrganization';
import { QuizForm } from '../../../components/quiz/QuizForm';

interface Question {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  order: number;
}

interface Quiz {
  id: string;
  title: string;
  description?: string;
  organizationId: string;
  visitorType: 'UMUM' | 'KARYAWAN';
  questions: Question[];
}

interface Video {
  id: string;
  title: string;
  youtubeUrl: string;
  organizationId: string;
  visitorType: 'UMUM' | 'KARYAWAN';
}

export function ManajemenKonten() {
  const { organization, loading: orgLoading } = useAdminOrganization();
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [showQuizForm, setShowQuizForm] = useState(false);
  const [editingQuiz, setEditingQuiz] = useState<Quiz | null>(null);
  const [quizFormVisitorType, setQuizFormVisitorType] = useState<'UMUM' | 'KARYAWAN'>('UMUM');
  const [saving, setSaving] = useState(false);

  // Fetch quizzes and videos when organization is available
  useEffect(() => {
    if (organization?.id) {
      fetchQuizzesAndVideos();
    }
  }, [organization?.id]);

  const fetchQuizzesAndVideos = async () => {
    if (!organization?.id) return;

    try {
      setLoading(true);

      // Fetch quizzes
      const quizResponse = await fetch(`/api/quiz/organizations/${organization.id}/quizzes`, {
        credentials: 'include'
      });
      const quizData = await quizResponse.json();

      if (quizData.success) {
        setQuizzes(quizData.data);
      }

      // Fetch videos
      const videoResponse = await fetch(`/api/video/organizations/${organization.id}/videos`, {
        credentials: 'include'
      });
      const videoData = await videoResponse.json();

      if (videoData.success) {
        setVideos(videoData.data);
      }
    } catch (error) {
      console.error('Error fetching quizzes and videos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuiz = async (quizId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus quiz ini?')) return;

    try {
      const response = await fetch(`/api/quiz/${quizId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();
      if (result.success) {
        fetchQuizzesAndVideos(); // Refresh data
      }
    } catch (error) {
      console.error('Error deleting quiz:', error);
    }
  };

  const handleDeleteVideo = async (videoId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus video ini?')) return;

    try {
      const response = await fetch(`/api/video/${videoId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();
      if (result.success) {
        fetchQuizzesAndVideos(); // Refresh data
      }
    } catch (error) {
      console.error('Error deleting video:', error);
    }
  };

  const handleCreateQuiz = (visitorType: 'UMUM' | 'KARYAWAN') => {
    setEditingQuiz(null);
    setQuizFormVisitorType(visitorType);
    setShowQuizForm(true);
  };

  const handleEditQuiz = (quiz: Quiz) => {
    setEditingQuiz(quiz);
    setQuizFormVisitorType(quiz.visitorType);
    setShowQuizForm(true);
  };

  const handleSaveQuiz = async (quizData: Omit<Quiz, 'id'>) => {
    try {
      setSaving(true);

      const url = editingQuiz ? `/api/quiz/${editingQuiz.id}` : '/api/quiz';
      const method = editingQuiz ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(quizData),
      });

      const result = await response.json();
      if (result.success) {
        setShowQuizForm(false);
        setEditingQuiz(null);
        fetchQuizzesAndVideos(); // Refresh data
      } else {
        alert('Gagal menyimpan quiz: ' + result.error);
      }
    } catch (error) {
      console.error('Error saving quiz:', error);
      alert('Terjadi kesalahan saat menyimpan quiz');
    } finally {
      setSaving(false);
    }
  };

  const handleCancelQuizForm = () => {
    setShowQuizForm(false);
    setEditingQuiz(null);
  };

  const VideoUploadSection = ({
    title,
    visitorType
  }: {
    title: string;
    visitorType: 'UMUM' | 'KARYAWAN';
  }) => {
    const [videoUrl, setVideoUrl] = useState('');
    const [videoTitle, setVideoTitle] = useState('');
    const [isUploading, setIsUploading] = useState(false);

    const handleVideoUpload = async () => {
      if (!videoUrl || !videoTitle || !organization?.id) return;

      try {
        setIsUploading(true);
        const response = await fetch('/api/video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Include cookies for authentication
          body: JSON.stringify({
            title: videoTitle,
            youtubeUrl: videoUrl,
            organizationId: organization.id,
            visitorType: visitorType
          }),
        });

        const result = await response.json();
        if (result.success) {
          setVideoUrl('');
          setVideoTitle('');
          fetchQuizzesAndVideos(); // Refresh data
        }
      } catch (error) {
        console.error('Error uploading video:', error);
      } finally {
        setIsUploading(false);
      }
    };

    const orgVideos = videos.filter(v => v.visitorType === visitorType);

    return (
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>

          {/* Add Video Form */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Judul Video
              </label>
              <Input
                value={videoTitle}
                onChange={(e) => setVideoTitle(e.target.value)}
                placeholder="Masukkan judul video"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL YouTube
              </label>
              <Input
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="https://www.youtube.com/watch?v=..."
              />
            </div>
            <Button
              onClick={handleVideoUpload}
              disabled={!videoUrl || !videoTitle || isUploading}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white"
            >
              <Upload className="h-4 w-4 mr-2" />
              {isUploading ? 'Menyimpan...' : 'Simpan Video'}
            </Button>
          </div>

          {/* Video List */}
          <div className="space-y-3">
            {orgVideos.map((video) => (
              <div key={video.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div>
                  <p className="font-medium text-gray-900">{video.title}</p>
                  <p className="text-sm text-gray-500">{video.youtubeUrl}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteVideo(video.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            {orgVideos.length === 0 && (
              <p className="text-sm text-gray-500 text-center py-4">
                Belum ada video untuk tipe pengunjung ini
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const QuizSection = ({
    visitorType
  }: {
    visitorType: 'UMUM' | 'KARYAWAN';
  }) => {
    const orgQuizzes = quizzes.filter(q => q.visitorType === visitorType);

    return (
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Quiz untuk Pengunjung Tipe {visitorType === 'KARYAWAN' ? 'Karyawan' : 'Umum'}
            </h3>
            <Button
              onClick={() => handleCreateQuiz(visitorType)}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Tambah Quiz
            </Button>
          </div>

          <div className="space-y-4">
            {orgQuizzes.map((quiz) => (
              <div key={quiz.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{quiz.title}</h4>
                    {quiz.description && (
                      <p className="text-sm text-gray-600 mt-1">{quiz.description}</p>
                    )}
                    <p className="text-sm text-gray-500 mt-2">
                      {quiz.questions.length} pertanyaan
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditQuiz(quiz)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteQuiz(quiz.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  {quiz.questions.slice(0, 2).map((question, index) => (
                    <div key={question.id} className="bg-gray-50 p-3 rounded">
                      <p className="text-sm font-medium text-gray-700">
                        {index + 1}. {question.question}
                      </p>
                      <div className="mt-2 space-y-1">
                        {question.options.map((option, optIndex) => (
                          <p key={optIndex} className={`text-xs ${optIndex === question.correctAnswer ? 'text-green-600 font-medium' : 'text-gray-500'}`}>
                            {String.fromCharCode(97 + optIndex)}. {option}
                          </p>
                        ))}
                      </div>
                    </div>
                  ))}
                  {quiz.questions.length > 2 && (
                    <p className="text-sm text-gray-500">
                      ... dan {quiz.questions.length - 2} pertanyaan lainnya
                    </p>
                  )}
                </div>
              </div>
            ))}

            {orgQuizzes.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Belum ada quiz untuk tipe pengunjung ini
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (orgLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-600">Organisasi tidak ditemukan</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen Konten</h1>
          <p className="text-gray-600 mt-1">
            Kelola konten video dan quiz untuk safety induction - {organization.name}
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Video Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <VideoUploadSection
            title="Video untuk Pengunjung Tipe Karyawan"
            visitorType="KARYAWAN"
          />
          <VideoUploadSection
            title="Video untuk Pengunjung Tipe Umum"
            visitorType="UMUM"
          />
        </div>

        {/* Quiz Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <QuizSection visitorType="KARYAWAN" />
          <QuizSection visitorType="UMUM" />
        </div>
      </div>

      {/* Quiz Form Modal */}
      {showQuizForm && organization && (
        <QuizForm
          quiz={editingQuiz}
          organizationId={organization.id}
          visitorType={quizFormVisitorType}
          onSave={handleSaveQuiz}
          onCancel={handleCancelQuizForm}
          loading={saving}
        />
      )}
    </div>
  );
}
