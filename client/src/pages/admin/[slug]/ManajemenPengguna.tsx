import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../../../components/ui/select';
import { Search, Loader2, AlertCircle } from 'lucide-react';
import { Input } from '../../../components/ui/input';
import { authService } from '../../../services/auth.service';
import type { OrganizationMemberDetail } from '../../../types/auth.types';

export function ManajemenPengguna() {
  const { slug } = useParams<{ slug: string }>();
  
  const [members, setMembers] = useState<OrganizationMemberDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState('10');

  // Load organization members
  useEffect(() => {
    const loadMembers = async () => {
      if (!slug) {
        console.log('❌ No slug provided');
        return;
      }
      
      console.log('🔍 Loading members for organization slug:', slug);
      
      try {
        setLoading(true);
        setError(null);
        
        // Step 1: Get organization by slug to get the ID
        console.log('📞 Step 1: Getting organization by slug:', slug);
        const orgResult = await authService.getOrganizationBySlug(slug);
        console.log('✅ Organization found:', orgResult);
        
        if (!orgResult || !orgResult.id) {
          throw new Error('Organization not found or missing ID');
        }
        
        // Step 2: Get organization members using the ID
        console.log('📞 Step 2: Getting organization members with ID:', orgResult.id);
        const result = await authService.getOrganizationMembers(orgResult.id) as unknown as OrganizationMemberDetail[];
        console.log('✅ Members loaded successfully:', {
          total: result?.length || 0,
          data: result
        });
        
        if (result && Array.isArray(result)) {
          result.forEach((member, index) => {
            console.log(`👤 Member ${index + 1}:`, {
              id: member.id,
              name: member.name,
              email: member.email,
              role: member.role,
              lastActive: (member as any).lastActive || 'N/A'
            });
          });
        }
        
        setMembers(result || []);
      } catch (err) {
        console.error('❌ Error loading organization members:', err);
        console.error('🔍 Error details:', {
          slug,
          error: err instanceof Error ? err.message : 'Unknown error',
          stack: err instanceof Error ? err.stack : undefined
        });
        setError('Gagal memuat data anggota organisasi');
      } finally {
        setLoading(false);
      }
    };

    loadMembers();
  }, [slug]);

  // Filter members based on search term
  const filteredMembers = members.filter(member => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      member.name.toLowerCase().includes(searchLower) ||
      member.email.toLowerCase().includes(searchLower) ||
      member.role.toLowerCase().includes(searchLower)
    );
  });

  // Pagination
  const itemsPerPage = parseInt(entriesPerPage);
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMembers = filteredMembers.slice(startIndex, endIndex);
  
  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen Pengguna</h1>
          <p className="text-gray-600 mt-1">Kelola anggota organisasi dan hak akses sistem</p>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Manajemen Pengguna</h1>
              <p className="text-sm text-gray-600 mt-1">
                Kelola anggota organisasi {slug}
              </p>
            </div>
          </div>

          <div className="mb-6">
            {/* Controls */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Show</span>
                  <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600">entries per pages</span>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Nama</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Terakhir Aktif</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={3} className="py-8 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-gray-500">Memuat data anggota...</span>
                      </div>
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={3} className="py-8 text-center">
                      <div className="flex items-center justify-center gap-2 text-red-500">
                        <AlertCircle className="h-4 w-4" />
                        <span>{error}</span>
                      </div>
                    </td>
                  </tr>
                ) : filteredMembers.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="py-8 text-center text-gray-500">
                      Tidak ada anggota yang sesuai dengan pencarian
                    </td>
                  </tr>
                ) : (
                  currentMembers.map((member) => (
                    <tr key={member.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{member.name}</div>
                          <div className="text-sm text-gray-500">{member.email}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {member.role}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-900">{(member as any).lastActive || 'N/A'}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-600">
              Menampilkan {filteredMembers.length > 0 ? ((currentPage - 1) * itemsPerPage) + 1 : 0} - {Math.min(currentPage * itemsPerPage, filteredMembers.length)} dari {filteredMembers.length} anggota
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || totalPages === 0}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700 px-3">
                Page {totalPages > 0 ? currentPage : 0} of {totalPages}
              </span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || totalPages === 0}
              >
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
