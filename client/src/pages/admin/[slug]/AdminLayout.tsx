import { useState, ReactNode } from 'react';
import { Header } from '../../../components/layout/Header';
import { AdminSidebar } from '../../../components/layout/AdminSidebar';
import { Organization } from '../../../types/auth.types';

interface AdminLayoutProps {
  children: ReactNode;
  organization?: Organization;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="h-screen overflow-hidden bg-gray-50">
      {/* Header */}
      <Header sidebarOpen={sidebarOpen} onToggleSidebar={toggleSidebar} />

      {/* Layout Container */}
      <div className="flex h-screen pt-16">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block">
          <AdminSidebar isOpen={true} onClose={closeSidebar} isMobile={false} />
        </div>

        {/* Mobile Sidebar */}
        <div className="lg:hidden">
          <AdminSidebar isOpen={sidebarOpen} onClose={closeSidebar} isMobile={true} />
        </div>

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            <div className="p-4 lg:p-6 min-h-full">
              <div className="max-w-full">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
