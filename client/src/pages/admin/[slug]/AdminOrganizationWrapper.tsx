import { ReactNode } from 'react';
import { useAdminOrganization } from '../../../hooks/useAdminOrganization';
import { AdminLayout } from './AdminLayout';

interface AdminOrganizationWrapperProps {
  children: ReactNode;
  redirectToSlug?: boolean;
}

/**
 * Wrapper component yang menangani organisasi context untuk admin
 * Memastikan admin memiliki akses ke organisasi yang sesuai dengan slug
 */
export function AdminOrganizationWrapper({ children, redirectToSlug = false }: AdminOrganizationWrapperProps) {
  const { organization, loading, error } = useAdminOrganization(redirectToSlug);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading organization...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h2 className="text-lg font-semibold text-red-800 mb-2">Access Error</h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md">
            <h2 className="text-lg font-semibold text-yellow-800 mb-2">No Organization</h2>
            <p className="text-yellow-600">No organization found for this admin.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout organization={organization}>
      {children}
    </AdminLayout>
  );
}