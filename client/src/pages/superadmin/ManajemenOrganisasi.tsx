import { useState } from 'react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Plus, Search } from 'lucide-react';
import { Organization } from '../../types/auth.types';
import { useOrganizations } from '../../hooks/useOrganizations';
import { OrganizationList } from '../../components/organizations/OrganizationList';
import { OrganizationCreateModal } from '../../components/organizations/OrganizationCreateModal';
import { OrganizationEditModal } from '../../components/organizations/OrganizationEditModal';
import { OrganizationDeleteModal } from '../../components/organizations/OrganizationDeleteModal';

interface OrganizationFormData {
  name: string;
  slug: string;
}

export function ManajemenOrganisasi() {
  const {
    organizations,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    createOrganization,
    updateOrganization,
    deleteOrganization,
    modalLoading,
    modalError
  } = useOrganizations();
  
  const [pageSize, setPageSize] = useState(10);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);

  // Modal handlers
   const handleCreateOrganization = async (data: OrganizationFormData): Promise<boolean> => {
     const success = await createOrganization(data);
     if (success) {
       setShowCreateModal(false);
     }
     return success;
   };

   const handleUpdateOrganization = async (id: string, data: OrganizationFormData): Promise<boolean> => {
     const success = await updateOrganization(id, data);
     if (success) {
       setShowEditModal(false);
       setSelectedOrganization(null);
     }
     return success;
   };

   const handleDeleteOrganization = async (id: string): Promise<boolean> => {
     const success = await deleteOrganization(id);
     if (success) {
       setShowDeleteModal(false);
       setSelectedOrganization(null);
     }
     return success;
   };

  const openEditModal = (organization: Organization) => {
    setSelectedOrganization(organization);
    setShowEditModal(true);
  };

  const openDeleteModal = (organization: Organization) => {
    setSelectedOrganization(organization);
    setShowDeleteModal(true);
  };

  return (
    <div className="p-4 lg:p-6 space-y-4 lg:space-y-6">
      {/* Page Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900">
          Pengaturan Sistem - Manajemen Organisasi
        </h1>
      </div>
     

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Cari organisasi..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Button 
          onClick={() => setShowCreateModal(true)}
          className="bg-orange-500 hover:bg-orange-600 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Tambah Organisasi
        </Button>
      </div>

      {/* Organizations List */}
       <OrganizationList
         organizations={organizations}
         loading={loading}
         error={error}
         onEdit={openEditModal}
         onDelete={openDeleteModal}
         pageSize={pageSize}
         onPageSizeChange={setPageSize}
       />

      {/* Modals */}
      <OrganizationCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateOrganization}
        loading={modalLoading}
        error={modalError}
      />

      <OrganizationEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSubmit={handleUpdateOrganization}
        organization={selectedOrganization}
        loading={modalLoading}
        error={modalError}
      />

      <OrganizationDeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteOrganization}
        organization={selectedOrganization}
        loading={modalLoading}
        error={modalError}
      />

    </div>
  );
}