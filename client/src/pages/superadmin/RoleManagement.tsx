import { useState } from 'react';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { User, Edit, Trash2, Loader2 } from 'lucide-react';
import { Modal } from '../../components/ui/modal';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Textarea } from '../../components/ui/textarea';
import { useRoles } from '../../hooks/useRoles';
import type { RoleWithDetails, UniqueRole, CreateRoleData, UpdateRoleData, CustomRoleWithPermissions, UpdateCustomRoleData } from '../../services/auth/role-management.service';

// Module types based on schema.prisma
const MODULE_TYPES = [
  { value: 'manajemen_pengunjung', label: 'Manaj<PERSON>en Pengunjung' },
  { value: 'pengaturan_sistem', label: 'Pengaturan Sistem' },
  { value: 'backup_restore', label: 'Backup & Restore' },
  { value: 'logs', label: 'Logs' },
  { value: 'profil', label: 'Profil' },
  { value: 'digital_permit', label: 'Digital Permit' }
] as const;

interface CustomPermission {
  moduleType: string;
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

interface CustomRoleFormData {
  name: string;
  description: string;
  organizationId: string;
  permissions: CustomPermission[];
}

interface RoleFormData {
  userId: string;
  organizationId: string;
  role: string;
}

export function RoleManagement() {
  const {
    roles,
    uniqueRoles,
    customRoles,
    availableUsers,
    availableOrganizations,
    loading,
    error,
    searchValue,
    currentPage,
    totalPages,
    setCurrentPage,
    createRole,
    updateRole,
    deleteRole,
    createCustomRole,
    updateCustomRole,
    deleteCustomRole,
    handleSearch,
    clearError,
    refreshData,
    useUniqueView
  } = useRoles();

  // Use unique roles data
  const displayRoles = useUniqueView ? uniqueRoles : roles;

  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCustomRoleModalOpen, setIsCustomRoleModalOpen] = useState(false);
  const [isEditCustomRoleModalOpen, setIsEditCustomRoleModalOpen] = useState(false);
  const [isDeleteCustomRoleModalOpen, setIsDeleteCustomRoleModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UniqueRole | null>(null);
  const [selectedCustomRole, setSelectedCustomRole] = useState<CustomRoleWithPermissions | null>(null);
  const [formData, setFormData] = useState<RoleFormData>({
    userId: '',
    organizationId: '',
    role: ''
  });
  const [customRoleData, setCustomRoleData] = useState<CustomRoleFormData>({
    name: '',
    description: '',
    organizationId: '',
    permissions: MODULE_TYPES.map(module => ({
      moduleType: module.value,
      canRead: false,
      canCreate: false,
      canUpdate: false,
      canDelete: false
    }))
  });
  const [formErrors, setFormErrors] = useState<Partial<RoleFormData>>({});
  const [customRoleErrors, setCustomRoleErrors] = useState<Partial<CustomRoleFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available role options
  const roleOptions = [
    { value: 'admin', label: 'Admin' },
    { value: 'member', label: 'Member' },
    { value: 'viewer', label: 'Viewer' }
  ];

  // Handle form input changes
  const handleInputChange = (field: keyof RoleFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Partial<RoleFormData> = {};
    
    if (!formData.userId) errors.userId = 'User harus dipilih';
    if (!formData.organizationId) errors.organizationId = 'Organisasi harus dipilih';
    if (!formData.role) errors.role = 'Role harus dipilih';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reset form
  const resetForm = () => {
    setFormData({ userId: '', organizationId: '', role: '' });
    setFormErrors({});
    setSelectedRole(null);
  };

  // Handle add role (commented out as not used)
  // const handleAddRole = () => {
  //   resetForm();
  //   setIsAddModalOpen(true);
  // };

  // Handle edit role
  const handleEditRole = (role: UniqueRole | RoleWithDetails) => {
    setSelectedRole(role as UniqueRole);
    if (useUniqueView) {
      const uniqueRole = role as UniqueRole;
      setFormData({
        userId: uniqueRole.userId,
        organizationId: uniqueRole.organizationId || '',
        role: uniqueRole.role
      });
    } else {
      const detailRole = role as RoleWithDetails;
      setFormData({
        userId: detailRole.user?.id || '',
        organizationId: detailRole.organization?.id || '',
        role: detailRole.role
      });
    }
    setIsEditModalOpen(true);
  };

  // Handle delete role
  const handleDeleteRole = (role: UniqueRole | RoleWithDetails) => {
    setSelectedRole(role as UniqueRole);
    setIsDeleteModalOpen(true);
  };

  // Submit create role
  const handleSubmitCreate = async () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    try {
      const createData: CreateRoleData = {
        userId: formData.userId,
        organizationId: formData.organizationId,
        role: formData.role
      };
      
      const response = await createRole(createData);
      if (response.success) {
        setIsAddModalOpen(false);
        resetForm();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit update role
  const handleSubmitUpdate = async () => {
    if (!selectedRole || !validateForm()) return;
    
    setIsSubmitting(true);
    try {
      const updateData: UpdateRoleData = {
        role: formData.role
      };
      
      const response = await updateRole(selectedRole.id, updateData);
      if (response.success) {
        setIsEditModalOpen(false);
        resetForm();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit delete role
  const handleSubmitDelete = async () => {
    if (!selectedRole) return;
    
    setIsSubmitting(true);
    try {
      const response = await deleteRole(selectedRole.id);
      if (response.success) {
        setIsDeleteModalOpen(false);
        resetForm();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Close modals
  const closeModals = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    setIsCustomRoleModalOpen(false);
    setIsEditCustomRoleModalOpen(false);
    setIsDeleteCustomRoleModalOpen(false);
    setSelectedRole(null);
    setSelectedCustomRole(null);
    setFormData({
      userId: '',
      organizationId: '',
      role: ''
    });
    setCustomRoleData({
        name: '',
        description: '',
        organizationId: '',
        permissions: MODULE_TYPES.map(module => ({
          moduleType: module.value,
          canRead: false,
          canCreate: false,
          canUpdate: false,
          canDelete: false
        }))
      });
    setFormErrors({});
    setCustomRoleErrors({});
    setIsSubmitting(false);
  };

  // Handle permission changes
  const handlePermissionChange = (moduleIndex: number, permission: keyof Omit<CustomPermission, 'module'>, value: boolean) => {
    setCustomRoleData(prev => ({
      ...prev,
      permissions: prev.permissions.map((perm, index) => 
        index === moduleIndex 
          ? { ...perm, [permission]: value }
          : perm
      )
    }));
  };

  // Handle custom role form submission
  const handleCustomRoleSubmit = async () => {
    setIsSubmitting(true);
    setCustomRoleErrors({});

    // Basic validation
    const errors: Partial<CustomRoleFormData> = {};
    if (!customRoleData.name.trim()) {
      errors.name = 'Nama role wajib diisi';
    }
    if (!customRoleData.organizationId) {
      errors.organizationId = 'Organisasi wajib dipilih';
    }

    if (Object.keys(errors).length > 0) {
      setCustomRoleErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      // Convert permissions to the format expected by the API
      const permissionsArray = customRoleData.permissions.map((permission, index) => ({
        moduleType: MODULE_TYPES[index].value,
        canCreate: permission.canCreate || false,
        canRead: permission.canRead || false,
        canUpdate: permission.canUpdate || false,
        canDelete: permission.canDelete || false
      }));

      const apiData = {
        name: customRoleData.name,
        description: customRoleData.description,
        organizationId: customRoleData.organizationId,
        permissions: permissionsArray
      };
      
      const response = await createCustomRole(apiData);
      
      if (response.success) {
        closeModals();
        await refreshData();
      } else {
        setCustomRoleErrors({ name: response.error || 'Gagal membuat custom role' });
      }
    } catch (error) {
      console.error('Error creating custom role:', error);
      setCustomRoleErrors({ name: 'Gagal membuat custom role' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit custom role
  const handleEditCustomRole = async (customRole: CustomRoleWithPermissions) => {
    setSelectedCustomRole(customRole);
    
    // Populate form with existing data
    // Note: Backend uses 'module' field, but frontend interface uses 'moduleType'
    const permissionsMap = new Map(customRole.permissions.map(p => [(p as any).module || p.moduleType, p]));
    const formPermissions = MODULE_TYPES.map(module => {
      const existing = permissionsMap.get(module.value);
      return {
        moduleType: module.value,
        canRead: existing?.canRead || false,
        canCreate: existing?.canCreate || false,
        canUpdate: existing?.canUpdate || false,
        canDelete: existing?.canDelete || false
      };
    });
    
    setCustomRoleData({
      name: customRole.name,
      description: customRole.description || '',
      organizationId: customRole.organizationId,
      permissions: formPermissions
    });
    
    setIsEditCustomRoleModalOpen(true);
  };

  // Handle update custom role
  const handleUpdateCustomRole = async () => {
    if (!selectedCustomRole) return;
    
    setIsSubmitting(true);
    setCustomRoleErrors({});

    // Basic validation
    const errors: Partial<CustomRoleFormData> = {};
    if (!customRoleData.name.trim()) {
      errors.name = 'Nama role wajib diisi';
    }
    if (!customRoleData.organizationId) {
      errors.organizationId = 'Organisasi wajib dipilih';
    }

    if (Object.keys(errors).length > 0) {
      setCustomRoleErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      const permissionsArray = customRoleData.permissions.map((permission, index) => ({
        moduleType: MODULE_TYPES[index].value,
        canCreate: permission.canCreate || false,
        canRead: permission.canRead || false,
        canUpdate: permission.canUpdate || false,
        canDelete: permission.canDelete || false
      }));

      const updateData: UpdateCustomRoleData = {
        name: customRoleData.name,
        description: customRoleData.description,
        permissions: permissionsArray
      };
      
      const response = await updateCustomRole(selectedCustomRole.id, updateData);
      
      if (response.success) {
        closeModals();
        await refreshData();
      } else {
        setCustomRoleErrors({ name: response.error || 'Gagal memperbarui custom role' });
      }
    } catch (error) {
      console.error('Error updating custom role:', error);
      setCustomRoleErrors({ name: 'Gagal memperbarui custom role' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete custom role
  const handleDeleteCustomRole = (customRole: CustomRoleWithPermissions) => {
    setSelectedCustomRole(customRole);
    setIsDeleteCustomRoleModalOpen(true);
  };

  // Submit delete custom role
  const handleSubmitDeleteCustomRole = async () => {
    if (!selectedCustomRole) return;
    
    setIsSubmitting(true);
    try {
      const response = await deleteCustomRole(selectedCustomRole.id);
      if (response.success) {
        closeModals();
        await refreshData();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Role</h1>
        <div className="flex gap-2">
          <Button 
            onClick={() => setIsCustomRoleModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <User className="w-4 h-4 mr-2" />
            Tambah Role
          </Button>
         
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex justify-between items-center">
            <p className="text-red-800">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="mb-6">
        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <Input
              placeholder="Cari role atau organisasi..."
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full"
            />
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Org
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center">
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        Memuat data...
                      </div>
                    </td>
                  </tr>
                ) : displayRoles.length === 0 && customRoles.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="px-6 py-4 text-center text-gray-500">
                      {searchValue ? 'Tidak ada role yang ditemukan' : 'Belum ada role'}
                    </td>
                  </tr>
                ) : (
                  <>
                    {/* Display regular roles */}
                    {displayRoles.map((role) => {
                      const roleDisplay = useUniqueView ? (role as UniqueRole) : (role as RoleWithDetails);
                      const roleName = useUniqueView 
                        ? (roleDisplay as UniqueRole).customRoleName || (roleDisplay as UniqueRole).role
                        : (roleDisplay as RoleWithDetails).role;
                      const orgName = useUniqueView 
                        ? (roleDisplay as UniqueRole).organizationName || '-'
                        : (roleDisplay as RoleWithDetails).organization?.name || '-';
                      
                      return (
                        <tr key={`role-${role.id}`} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              roleName === 'superadmin' ? 'bg-purple-100 text-purple-800' :
                              roleName === 'admin' ? 'bg-red-100 text-red-800' :
                              roleName === 'member' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {roleName}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {orgName}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditRole(role)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteRole(role)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                    
                    {/* Display custom roles */}
                    {customRoles.map((customRole) => {
                      const orgName = availableOrganizations.find(org => org.id === customRole.organizationId)?.name || '-';
                      
                      return (
                        <tr key={`custom-${customRole.id}`} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              {customRole.name} 
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {orgName}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditCustomRole(customRole)}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteCustomRole(customRole)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-6">
        <div className="text-sm text-gray-700">
          Menampilkan {displayRoles.length > 0 ? ((currentPage - 1) * 10) + 1 : 0} - {Math.min(currentPage * 10, displayRoles.length)} dari {displayRoles.length} role
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            disabled={currentPage <= 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            Sebelumnya
          </Button>
          <Button 
            variant="outline" 
            disabled={currentPage >= totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            Selanjutnya
          </Button>
        </div>
      </div>

      {/* Add Role Modal */}
      <Modal isOpen={isAddModalOpen} onClose={closeModals} title="Tambah Role">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">User</label>
            <Select value={formData.userId} onValueChange={(value) => handleInputChange('userId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih user" />
              </SelectTrigger>
              <SelectContent>
                {availableUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.name} ({user.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.userId && (
              <p className="text-red-500 text-sm mt-1">{formErrors.userId}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Organisasi</label>
            <Select value={formData.organizationId} onValueChange={(value) => handleInputChange('organizationId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih organisasi" />
              </SelectTrigger>
              <SelectContent>
                {availableOrganizations.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.organizationId && (
              <p className="text-red-500 text-sm mt-1">{formErrors.organizationId}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Role</label>
            <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih role" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((role) => (
                  <SelectItem key={role.value} value={role.value}>
                    {role.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.role && (
              <p className="text-red-500 text-sm mt-1">{formErrors.role}</p>
            )}
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={closeModals} disabled={isSubmitting}>
            Batal
          </Button>
          <Button onClick={handleSubmitCreate} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Menyimpan...
              </>
            ) : (
              'Simpan'
            )}
          </Button>
        </div>
      </Modal>

      {/* Edit Role Modal */}
      <Modal isOpen={isEditModalOpen} onClose={closeModals} title="Edit Role">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">User</label>
            <Input
              value={useUniqueView ? (selectedRole?.userName || '') : (selectedRole as any)?.user?.name || ''}
              disabled
              className="bg-gray-50"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Organisasi</label>
            <Input
              value={useUniqueView ? (selectedRole?.organizationName || '') : (selectedRole as any)?.organization?.name || ''}
              disabled
              className="bg-gray-50"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Role</label>
            <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih role" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((role) => (
                  <SelectItem key={role.value} value={role.value}>
                    {role.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {formErrors.role && (
              <p className="text-red-500 text-sm mt-1">{formErrors.role}</p>
            )}
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={closeModals} disabled={isSubmitting}>
            Batal
          </Button>
          <Button onClick={handleSubmitUpdate} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Menyimpan...
              </>
            ) : (
              'Simpan'
            )}
          </Button>
        </div>
      </Modal>

      {/* Delete Role Modal */}
      <Modal isOpen={isDeleteModalOpen} onClose={closeModals} title="Hapus Role">
        <p className="text-gray-600 mb-6">
          Apakah Anda yakin ingin menghapus role <strong>{selectedRole?.role}</strong> dari user <strong>{useUniqueView ? (selectedRole?.userName || '') : (selectedRole as any)?.user?.name || ''}</strong> di organisasi <strong>{useUniqueView ? (selectedRole?.organizationName || '') : (selectedRole as any)?.organization?.name || ''}</strong>?
        </p>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={closeModals} disabled={isSubmitting}>
            Batal
          </Button>
          <Button variant="destructive" onClick={handleSubmitDelete} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Menghapus...
              </>
            ) : (
              'Hapus'
            )}
          </Button>
        </div>
      </Modal>

      {/* Edit Custom Role Modal */}
      <Modal
        isOpen={isEditCustomRoleModalOpen}
        onClose={closeModals}
        title="Edit Role"
        size="xl"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nama Role <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                placeholder="Masukkan nama role"
                value={customRoleData.name}
                onChange={(e) => setCustomRoleData(prev => ({ ...prev, name: e.target.value }))}
                className={customRoleErrors.name ? 'border-red-500' : ''}
              />
              {customRoleErrors.name && (
                <p className="text-red-500 text-sm mt-1">{customRoleErrors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Perusahaan
              </label>
              <Select
                value={customRoleData.organizationId}
                onValueChange={(value) => setCustomRoleData(prev => ({ ...prev, organizationId: value }))}
              >
                <SelectTrigger className={customRoleErrors.organizationId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Masukkan nama perusahaan" />
                </SelectTrigger>
                <SelectContent>
                  {availableOrganizations.map((org) => (
                    <SelectItem key={org.id} value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {customRoleErrors.organizationId && (
                <p className="text-red-500 text-sm mt-1">{customRoleErrors.organizationId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Deskripsi Role
              </label>
              <Textarea
                placeholder="Masukkan nama perusahaan"
                value={customRoleData.description}
                onChange={(e) => setCustomRoleData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          {/* Permissions Table */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Kapabilitas</h3>
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Create
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Read
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Update
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Delete
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {MODULE_TYPES.map((module, index) => (
                    <tr key={module.value} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {module.label}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canCreate || false}
                          onChange={(e) => handlePermissionChange(index, 'canCreate', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canRead || false}
                          onChange={(e) => handlePermissionChange(index, 'canRead', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canUpdate || false}
                          onChange={(e) => handlePermissionChange(index, 'canUpdate', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canDelete || false}
                          onChange={(e) => handlePermissionChange(index, 'canDelete', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={closeModals}
              disabled={isSubmitting}
            >
              Batal
            </Button>
            <Button
              onClick={handleUpdateCustomRole}
              disabled={isSubmitting}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                'Update'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Custom Role Modal */}
      <Modal isOpen={isDeleteCustomRoleModalOpen} onClose={closeModals} title="Hapus Custom Role">
        <p className="text-gray-600 mb-6">
          Apakah Anda yakin ingin menghapus custom role <strong>{selectedCustomRole?.name}</strong>? Tindakan ini tidak dapat dibatalkan.
        </p>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={closeModals} disabled={isSubmitting}>
            Batal
          </Button>
          <Button variant="destructive" onClick={handleSubmitDeleteCustomRole} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Menghapus...
              </>
            ) : (
              'Hapus'
            )}
          </Button>
        </div>
      </Modal>

      {/* Custom Role Modal */}
      <Modal
        isOpen={isCustomRoleModalOpen}
        onClose={closeModals}
        title="Tambah Role"
        size="xl"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nama Role <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                placeholder="Masukkan nama role"
                value={customRoleData.name}
                onChange={(e) => setCustomRoleData(prev => ({ ...prev, name: e.target.value }))}
                className={customRoleErrors.name ? 'border-red-500' : ''}
              />
              {customRoleErrors.name && (
                <p className="text-red-500 text-sm mt-1">{customRoleErrors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Perusahaan
              </label>
              <Select
                value={customRoleData.organizationId}
                onValueChange={(value) => setCustomRoleData(prev => ({ ...prev, organizationId: value }))}
              >
                <SelectTrigger className={customRoleErrors.organizationId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Masukkan nama perusahaan" />
                </SelectTrigger>
                <SelectContent>
                  {availableOrganizations.map((org) => (
                    <SelectItem key={org.id} value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {customRoleErrors.organizationId && (
                <p className="text-red-500 text-sm mt-1">{customRoleErrors.organizationId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Deskripsi Role
              </label>
              <Textarea
                placeholder="Masukkan nama perusahaan"
                value={customRoleData.description}
                onChange={(e) => setCustomRoleData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          {/* Permissions Table */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Kapabilitas</h3>
            <div className="border rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Create
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Read
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Update
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Delete
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {MODULE_TYPES.map((module, index) => (
                    <tr key={module.value} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {module.label}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canCreate || false}
                          onChange={(e) => handlePermissionChange(index, 'canCreate', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canRead || false}
                          onChange={(e) => handlePermissionChange(index, 'canRead', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canUpdate || false}
                          onChange={(e) => handlePermissionChange(index, 'canUpdate', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <input
                          type="checkbox"
                          checked={customRoleData.permissions[index]?.canDelete || false}
                          onChange={(e) => handlePermissionChange(index, 'canDelete', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={closeModals}
              disabled={isSubmitting}
            >
              Batal
            </Button>
            <Button
              onClick={handleCustomRoleSubmit}
              disabled={isSubmitting}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                'Tambah'
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}



