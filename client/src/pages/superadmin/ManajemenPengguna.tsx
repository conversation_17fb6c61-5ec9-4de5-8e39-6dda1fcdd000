import { useState, useEffect } from 'react';
import { Card, CardContent } from '../../components/ui/card';

// Custom Hooks
import { useUsers, UserWithOrganization } from '../../hooks/user-management/useUsers';
import { useOrganizations } from '../../hooks/user-management/useOrganizations';
import { useUserForm } from '../../hooks/user-management/useUserForm';
import { usePagination } from '../../hooks/user-management/usePagination';

// Services
import { roleManagementService, CustomRoleWithPermissions } from '../../services/auth/role-management.service';

// Components
import { SearchAndFilters } from '../../components/user-management/SearchAndFilters';
import { UserTable } from '../../components/user-management/UserTable';
import { Pagination } from '../../components/user-management/Pagination';
import { CreateUserModal } from '../../components/user-management/UserModals/CreateUserModal';
import { EditUserModal } from '../../components/user-management/UserModals/EditUserModal';
import { DeleteUserModal } from '../../components/user-management/UserModals/DeleteUserModal';

export function ManajemenPengguna() {
  // Pagination hook
  const {
    searchQuery,
    pageSize,
    currentPage,
    handleSearchChange,
    handlePageSizeChange,
    nextPage,
    prevPage
  } = usePagination();

  // Users hook
  const {
    users,
    loading,
    createUser,
    updateUser,
    deleteUser
  } = useUsers(searchQuery, pageSize, currentPage);

  // Organizations hook
  const { organizations } = useOrganizations();

  // Form hook
  const {
    formData,
    formErrors,
    submitting,
    setFormData,
    setFormErrors,
    setSubmitting,
    resetForm,
    validateForm,
    populateFormForEdit
  } = useUserForm();

  // Custom roles state
  const [customRoles, setCustomRoles] = useState<CustomRoleWithPermissions[]>([]);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithOrganization | null>(null);

  // Load custom roles
  useEffect(() => {
    const loadCustomRoles = async () => {
      try {
        const result = await roleManagementService.listCustomRoles();
        setCustomRoles(result.customRoles);
      } catch (error) {
        console.error('Failed to load custom roles:', error);
      }
    };

    loadCustomRoles();
  }, []);

  // Modal handlers
  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (user: UserWithOrganization) => {
    setSelectedUser(user);
    populateFormForEdit(user);
    setShowEditModal(true);
  };

  const openDeleteModal = (user: UserWithOrganization) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  // Form submission handlers
  const handleCreateUser = async () => {
    if (!validateForm()) return;
    
    setSubmitting(true);
    try {
      await createUser(formData);
      setShowCreateModal(false);
      resetForm();
    } catch (error: any) {
      setFormErrors({ submit: error.message || 'Gagal membuat pengguna' });
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!selectedUser || !validateForm(true)) return;
    
    setSubmitting(true);
    try {
      await updateUser(selectedUser.id, formData);
      setShowEditModal(false);
      resetForm();
      setSelectedUser(null);
    } catch (error: any) {
      setFormErrors({ submit: error.message || 'Gagal memperbarui pengguna' });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    setSubmitting(true);
    try {
      await deleteUser(selectedUser.id);
      setShowDeleteModal(false);
      setSelectedUser(null);
    } catch (error: any) {
      // Handle error if needed
    } finally {
      setSubmitting(false);
    }
  };

  // Close modal handlers
  const closeCreateModal = () => {
    setShowCreateModal(false);
    resetForm();
  };

  const closeEditModal = () => {
    setShowEditModal(false);
    resetForm();
    setSelectedUser(null);
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
    setSelectedUser(null);
  };





  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Pengguna</h1>
      </div>

      <Card>
        <CardContent className="p-6">
          <SearchAndFilters
            searchQuery={searchQuery}
            pageSize={pageSize}
            onSearchChange={handleSearchChange}
            onPageSizeChange={handlePageSizeChange}
            onCreateUser={openCreateModal}
          />

          <UserTable
             users={users}
             loading={loading}
             searchQuery={searchQuery}
             onEditUser={openEditModal}
             onDeleteUser={openDeleteModal}
           />

          <Pagination
            currentPage={currentPage}
            pageSize={pageSize}
            totalItems={users.length}
            loading={loading}
            onNextPage={nextPage}
            onPrevPage={prevPage}
          />
        </CardContent>
      </Card>

      <CreateUserModal
        isOpen={showCreateModal}
        onClose={closeCreateModal}
        formData={formData}
        formErrors={formErrors}
        submitting={submitting}
        organizations={organizations}
        customRoles={customRoles}
        onFormDataChange={setFormData}
        onSubmit={handleCreateUser}
      />

      <EditUserModal
         isOpen={showEditModal}
         onClose={closeEditModal}
         formData={formData}
         formErrors={formErrors}
         submitting={submitting}
         organizations={organizations}
         customRoles={customRoles}
         onFormDataChange={setFormData}
         onSubmit={handleUpdateUser}
       />

      <DeleteUserModal
        isOpen={showDeleteModal}
        onClose={closeDeleteModal}
        user={selectedUser}
        submitting={submitting}
        onConfirm={handleDeleteUser}
      />
    </div>
  );
}
