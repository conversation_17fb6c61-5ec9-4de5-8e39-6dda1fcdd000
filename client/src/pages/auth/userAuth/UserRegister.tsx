import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { PAMLogo } from '../../../components/ui/PAMLogo';
import { Camera, Upload } from 'lucide-react';
// import { signUp } from '../../../lib/auth-client'; // Not used, using custom registration API

interface Organization {
  id: string;
  name: string;
  slug: string;
}

export function UserRegister() {
  const [formData, setFormData] = useState({
    namaLengkap: '',
    username: '',
    password: '',
    nomorTelepon: '',
    email: '',
    tipePengunjung: 'umum' as 'umum' | 'karyawan',
    organizationId: ''
  });
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentStep, setCurrentStep] = useState(1); // 1: form, 2: verification, 3: success
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Fetch organizations on component mount
  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await fetch('/api/organizations', {
          credentials: 'include'
        });
        const data = await response.json();
        if (data.success) {
          setOrganizations(data.data);
        }
      } catch (error) {
        console.error('Error fetching organizations:', error);
      }
    };

    fetchOrganizations();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (!formData.namaLengkap || !formData.email || !formData.password || !formData.organizationId) {
      setError('Nama lengkap, email, password, dan organisasi wajib diisi');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError('Password minimal 6 karakter');
      setIsLoading(false);
      return;
    }

    try {
      // Use custom registration API
      const response = await fetch('/api/auth/register-visitor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          name: formData.namaLengkap,
          nomorTelepon: formData.nomorTelepon,
          tipePengunjung: formData.tipePengunjung,
          organizationId: formData.organizationId
        }),
      });

      const result = await response.json();

      if (!result.success) {
        setError(result.error || 'Registrasi gagal');
        setIsLoading(false);
        return;
      }

      // If successful, go to verification step (simulate)
      setCurrentStep(2);
    } catch (error) {
      console.error('Registration error:', error);
      setError('Terjadi kesalahan yang tidak terduga');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate verification
    setTimeout(() => {
      setCurrentStep(3);
      setIsLoading(false);
    }, 1000);
  };

  const handleContinue = () => {
    // After successful registration, redirect to login
    navigate('/user/login', {
      state: { message: 'Registrasi berhasil! Silakan login dengan akun Anda.' }
    });
  };

  const handleFileUpload = (type: 'id' | 'selfie') => {
    // Dummy file upload handler
    console.log(`Upload ${type} clicked`);
  };

  // Step 1: Registration Form
  if (currentStep === 1) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Fixed mobile layout */}
        <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
          {/* Header with Logo */}
          <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
            <PAMLogo width={108} height={108} />
          </div>

          {/* Form Content */}
          <div className="flex-1 px-6 pb-6">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 text-left">REGISTER</h1>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-6 text-sm">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Nama Lengkap */}
              <div>
                <Input
                  type="text"
                  value={formData.namaLengkap}
                  onChange={(e) => handleInputChange('namaLengkap', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Nama Lengkap"
                />
              </div>

              {/* Username */}
              <div>
                <Input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Username"
                />
              </div>

              {/* Password */}
              <div>
                <Input
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Password"
                />
              </div>

              {/* Nomor Telepon */}
              <div>
                <Input
                  type="tel"
                  value={formData.nomorTelepon}
                  onChange={(e) => handleInputChange('nomorTelepon', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Nomor Telepon (WhatsApp)"
                />
              </div>

              {/* Email */}
              <div>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Email"
                />
              </div>

              {/* Organisasi */}
              <div>
                <label className="block text-base font-medium text-gray-900 mb-2">
                  Organisasi/Perusahaan
                </label>
                <select
                  value={formData.organizationId}
                  onChange={(e) => handleInputChange('organizationId', e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                >
                  <option value="">Pilih Organisasi</option>
                  {organizations.map((org) => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Tipe Pengunjung */}
              <div className="space-y-4">
                <label className="block text-base font-medium text-gray-900">
                  Tipe Pengunjung
                </label>
                <div className="flex space-x-8">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="tipePengunjung"
                      value="umum"
                      checked={formData.tipePengunjung === 'umum'}
                      onChange={(e) => handleInputChange('tipePengunjung', e.target.value)}
                      className="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-base text-gray-900">Umum</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="tipePengunjung"
                      value="karyawan"
                      checked={formData.tipePengunjung === 'karyawan'}
                      onChange={(e) => handleInputChange('tipePengunjung', e.target.value)}
                      className="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-base text-gray-900">Karyawan</span>
                  </label>
                </div>
              </div>

              {/* File Upload Buttons */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-base font-medium text-gray-900">Unggah ID Card:</span>
                  <Button
                    type="button"
                    onClick={() => handleFileUpload('id')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Unggah
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-base font-medium text-gray-900">Ambil Selfie</span>
                  <Button
                    type="button"
                    onClick={() => handleFileUpload('selfie')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium"
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    Ambil
                  </Button>
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
                >
                  {isLoading ? 'Mendaftar...' : 'Daftar'}
                </Button>
              </div>
            </form>

            {/* Login Link */}
            <div className="text-center mt-6">
              <p className="text-sm text-gray-600">
                Sudah punya akun?{' '}
                <button
                  onClick={() => navigate('/user/login')}
                  className="text-orange-600 hover:text-orange-500 font-medium"
                >
                  Login
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Step 2: Verification
  if (currentStep === 2) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
          {/* Header with Logo */}
          <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
            <PAMLogo width={108} height={108} />
          </div>

          {/* Verification Content */}
          <div className="flex-1 px-6 pb-6">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 text-left mb-6">REGISTER</h1>
              <div className="text-base text-gray-600 space-y-1">
                <p>Masukkan kode 6-digit yang telah</p>
                <p>dikirimkan ke:</p>
                <p className="font-medium text-gray-900"><EMAIL></p>
              </div>
            </div>

            <form onSubmit={handleVerification} className="space-y-8">
              <div>
                <Input
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  required
                  maxLength={6}
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center text-xl tracking-widest text-gray-500"
                  placeholder="######"
                />
              </div>

              <div className="text-center">
                <button
                  type="button"
                  className="text-orange-600 hover:text-orange-500 text-base font-medium"
                >
                  Minta Kode Baru
                </button>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                {isLoading ? 'Memverifikasi...' : 'Konfirmasi'}
              </Button>
            </form>

            <div className="text-center mt-6">
              <p className="text-sm text-gray-600">
                Sudah punya akun?{' '}
                <button
                  onClick={() => navigate('/user/login')}
                  className="text-orange-600 hover:text-orange-500 font-medium"
                >
                  Login
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Step 3: Success
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header with Logo */}
        <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
          <PAMLogo width={108} height={108} />
        </div>

        {/* Success Content */}
        <div className="flex-1 px-6 pb-6">
          <div className="text-left mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">REGISTER</h1>
            <p className="text-base text-gray-600">Registrasi berhasil!</p>
          </div>

          <Button
            onClick={handleContinue}
            className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
          >
            Lanjutkan
          </Button>
        </div>
      </div>
    </div>
  );
}
