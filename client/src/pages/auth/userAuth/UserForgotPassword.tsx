import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { PAMLogo } from '../../../components/ui/PAMLogo';

export function UserForgotPassword() {
  const [email, setEmail] = useState('');
  const [currentStep, setCurrentStep] = useState(1); // 1: input email, 2: success
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    // Simulate API call
    setTimeout(() => {
      setCurrentStep(2);
      setIsLoading(false);
    }, 1000);
  };

  const handleBackToLogin = () => {
    navigate('/user/login');
  };

  const handleCreateAccount = () => {
    navigate('/user/visitor-register');
  };

  // Step 1: Input Email
  if (currentStep === 1) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Fixed mobile layout */}
        <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
          {/* Header with Logo */}
          <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
            <PAMLogo width={108} height={108} />
          </div>

          {/* Form Content */}
          <div className="flex-1 px-6 pb-6">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 text-left mb-6">LUPA PASSWORD</h1>
              <p className="text-base text-gray-600 text-center leading-relaxed">
                Masukkan email untuk mendapatkan link reset password.
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg mb-6 text-sm">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Email Field */}
              <div>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full h-14 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-500"
                  placeholder="Email"
                />
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
              >
                {isLoading ? 'Mengirim...' : 'Kirim Link Verifikasi'}
              </Button>
            </form>

            {/* Action Links */}
            <div className="mt-8 space-y-4">
              <div className="text-center">
                <button
                  onClick={handleCreateAccount}
                  className="text-orange-600 hover:text-orange-500 text-base font-medium"
                >
                  Buat akun baru saja
                </button>
              </div>

              <div className="text-center">
                <Button
                  onClick={handleBackToLogin}
                  variant="outline"
                  className="w-full h-14 border-2 border-orange-600 text-orange-600 hover:bg-orange-50 font-medium rounded-lg transition-colors text-base"
                >
                  Kembali ke Login
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Step 2: Success Message
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Fixed mobile layout */}
      <div className="w-full max-w-sm mx-auto bg-white min-h-screen flex flex-col">
        {/* Header with Logo */}
        <div className="flex-shrink-0 pt-12 pb-8 px-6 text-center">
          <PAMLogo width={108} height={108} />
        </div>

        {/* Success Content */}
        <div className="flex-1 px-6 pb-6">
          <div className="mb-12">
            <h1 className="text-3xl font-bold text-gray-900 text-left mb-8">LUPA PASSWORD</h1>
            <p className="text-base text-gray-600">
              Link reset password telah dikirim!
            </p>
          </div>

          <Button
            onClick={handleBackToLogin}
            className="w-full h-14 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors text-base"
          >
            Login
          </Button>
        </div>
      </div>
    </div>
  );
}
