import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SignupForm } from '../../components/auth/SignupForm';
import { useSession } from '../../lib/auth-client';

export function SignupPage() {
  const { data: session, isPending } = useSession();
  const navigate = useNavigate();
  
  const isAuthenticated = !!session?.user;
  const user = session?.user;

  useEffect(() => {
    if (!isPending && isAuthenticated && user) {
      // Redirect based on user role
      if (user.role === 'superadmin') {
        navigate('/superadmin/dashboard', { replace: true });
      } else if (user.role === 'admin') {
        navigate('/admin/dashboard', { replace: true });
      } else {
        navigate('/user/quiz', { replace: true });
      }
    }
  }, [isPending, isAuthenticated, user, navigate]);

  return <SignupForm />;
}
