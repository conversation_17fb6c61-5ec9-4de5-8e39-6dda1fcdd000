-- Test data untuk testing flow aplikasi

-- Insert sample organizations (jika belum ada)
INSERT INTO "organization" (id, name, slug, "createdAt") VALUES 
('org_test_001', 'PT. Test Company A', 'test-company-a', NOW()),
('org_test_002', 'PT. Test Company B', 'test-company-b', NOW()),
('org_test_003', 'PT. Test Company C', 'test-company-c', NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample videos
INSERT INTO "video" (id, title, "youtubeUrl", "organizationId", "visitorType", "createdAt", "updatedAt") VALUES 
('video_001', 'Safety Induction Video - Karyawan Test A', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'org_test_001', 'KARYAWAN', NOW(), NOW()),
('video_002', 'Safety Induction Video - Umum Test A', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'org_test_001', 'UMUM', NOW(), NOW()),
('video_003', 'Safety Induction Video - Karyawan Test B', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'org_test_002', 'KARYAWAN', NOW(), NOW()),
('video_004', 'Safety Induction Video - Umum Test B', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'org_test_002', 'UMUM', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample quizzes
INSERT INTO "quiz" (id, title, description, "organizationId", "visitorType", "isActive", "createdAt", "updatedAt") VALUES 
('quiz_001', 'Quiz Keselamatan Karyawan - Test A', 'Quiz keselamatan untuk karyawan PT. Test Company A', 'org_test_001', 'KARYAWAN', true, NOW(), NOW()),
('quiz_002', 'Quiz Keselamatan Umum - Test A', 'Quiz keselamatan untuk pengunjung umum PT. Test Company A', 'org_test_001', 'UMUM', true, NOW(), NOW()),
('quiz_003', 'Quiz Keselamatan Karyawan - Test B', 'Quiz keselamatan untuk karyawan PT. Test Company B', 'org_test_002', 'KARYAWAN', true, NOW(), NOW()),
('quiz_004', 'Quiz Keselamatan Umum - Test B', 'Quiz keselamatan untuk pengunjung umum PT. Test Company B', 'org_test_002', 'UMUM', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample questions for quiz_001 (Karyawan Test A)
INSERT INTO "question" (id, question, options, "correctAnswer", "quizId", "order", "createdAt", "updatedAt") VALUES 
('q001_001', 'Apa tujuan utama dari safety induction untuk karyawan?', 
 '["Memberikan informasi jadwal kerja", "Memberikan pemahaman tentang keselamatan dan prosedur kerja", "Menyambut karyawan baru secara formal", "Menilai kemampuan fisik karyawan"]', 
 1, 'quiz_001', 0, NOW(), NOW()),
('q001_002', 'APD yang wajib digunakan di area kerja adalah?', 
 '["Helm, sepatu safety, rompi reflektif", "Hanya helm saja", "Masker dan sarung tangan saja", "Kacamata safety saja"]', 
 0, 'quiz_001', 1, NOW(), NOW()),
('q001_003', 'Apa yang harus dilakukan saat mendengar alarm darurat?', 
 '["Melanjutkan pekerjaan", "Mencari tahu penyebab alarm", "Segera menuju titik kumpul terdekat", "Menunggu instruksi dari supervisor"]', 
 2, 'quiz_001', 2, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample questions for quiz_002 (Umum Test A)
INSERT INTO "question" (id, question, options, "correctAnswer", "quizId", "order", "createdAt", "updatedAt") VALUES 
('q002_001', 'Sebagai pengunjung, apa yang harus dilakukan saat memasuki area kerja?', 
 '["Langsung masuk tanpa pemberitahuan", "Menggunakan APD yang disediakan dan mengikuti pendamping", "Mengambil foto untuk dokumentasi", "Berbicara dengan pekerja di area tersebut"]', 
 1, 'quiz_002', 0, NOW(), NOW()),
('q002_002', 'Jika melihat kondisi tidak aman, pengunjung harus?', 
 '["Mengabaikannya jika tidak berbahaya", "Mengambil foto sebagai bukti saja", "Melaporkan segera kepada pendamping", "Menunggu hingga orang lain melaporkannya"]', 
 2, 'quiz_002', 1, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample questions for quiz_003 (Karyawan Test B)
INSERT INTO "question" (id, question, options, "correctAnswer", "quizId", "order", "createdAt", "updatedAt") VALUES 
('q003_001', 'Prosedur keselamatan yang paling penting di Test Company B adalah?', 
 '["Memakai seragam yang rapi", "Mengikuti semua protokol keselamatan yang ditetapkan", "Datang tepat waktu", "Membawa kartu identitas"]', 
 1, 'quiz_003', 0, NOW(), NOW()),
('q003_002', 'Dalam situasi darurat, prioritas utama adalah?', 
 '["Menyelamatkan peralatan", "Keselamatan diri dan rekan kerja", "Melaporkan ke atasan", "Mematikan semua mesin"]', 
 1, 'quiz_003', 1, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample questions for quiz_004 (Umum Test B)
INSERT INTO "question" (id, question, options, "correctAnswer", "quizId", "order", "createdAt", "updatedAt") VALUES 
('q004_001', 'Pengunjung di Test Company B wajib?', 
 '["Membawa kamera", "Mengikuti aturan keselamatan yang diberikan", "Memakai pakaian formal", "Membawa dokumen identitas"]', 
 1, 'quiz_004', 0, NOW(), NOW()),
('q004_002', 'Area yang dilarang untuk pengunjung adalah?', 
 '["Area kantor", "Area produksi tanpa pendamping", "Area parkir", "Area resepsionis"]', 
 1, 'quiz_004', 1, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;
