import { Hono } from 'hono';
import { cors } from 'hono/cors';
import http from 'http';
import { corsConfig } from './src/config/cors.config.js';
import { dashboardRoutes } from './src/routes/dashboard.routes.js';

const app = new Hono()
  .use(cors(corsConfig))
  .get('/health', (c) => c.json({ status: 'ok' }))
  
  // Mount dashboard routes
  .route('/dashboard', dashboardRoutes);

const port = process.env.PORT || 3000;

// Simple HTTP server
const server = http.createServer(async (req, res) => {
  try {
    const url = `http://localhost:${port}${req.url}`;
    
    let body = '';
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      await new Promise((resolve) => {
        req.on('data', chunk => body += chunk);
        req.on('end', resolve);
      });
    }
    
    const request = new Request(url, {
      method: req.method,
      headers: req.headers,
      body: body || undefined,
    });
    
    const response = await app.fetch(request);
    
    res.statusCode = response.status;
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    
    const responseBody = await response.text();
    res.end(responseBody);
  } catch (error) {
    console.error('Error:', error);
    res.statusCode = 500;
    res.end('Internal Server Error');
  }
});

server.listen(port, () => {
  console.log(`🚀 Server running on port ${port}`);
});
