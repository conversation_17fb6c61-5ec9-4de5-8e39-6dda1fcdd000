/*
  Warnings:

  - You are about to drop the column `createdAt` on the `invitation` table. All the data in the column will be lost.
  - You are about to drop the column `teamId` on the `invitation` table. All the data in the column will be lost.
  - You are about to drop the column `activeTeamId` on the `session` table. All the data in the column will be lost.
  - You are about to drop the `team` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `team_member` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "invitation" DROP CONSTRAINT "invitation_teamId_fkey";

-- DropForeignKey
ALTER TABLE "team" DROP CONSTRAINT "team_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "team_member" DROP CONSTRAINT "team_member_memberId_fkey";

-- DropForeignKey
ALTER TABLE "team_member" DROP CONSTRAINT "team_member_teamId_fkey";

-- DropIndex
DROP INDEX "member_userId_organizationId_key";

-- AlterTable
ALTER TABLE "invitation" DROP COLUMN "createdAt",
DROP COLUMN "teamId",
ALTER COLUMN "role" DROP NOT NULL,
ALTER COLUMN "role" DROP DEFAULT,
ALTER COLUMN "status" DROP DEFAULT;

-- AlterTable
ALTER TABLE "member" ALTER COLUMN "role" DROP DEFAULT,
ALTER COLUMN "createdAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "organization" ALTER COLUMN "slug" DROP NOT NULL,
ALTER COLUMN "metadata" DROP DEFAULT,
ALTER COLUMN "metadata" SET DATA TYPE TEXT,
ALTER COLUMN "createdAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "session" DROP COLUMN "activeTeamId";

-- DropTable
DROP TABLE "team";

-- DropTable
DROP TABLE "team_member";
