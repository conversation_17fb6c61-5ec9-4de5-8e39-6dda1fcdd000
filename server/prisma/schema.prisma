
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  banned        Boolean   @default(false)
  banReason     String?
  banExpires    DateTime?
  role          String    @default("user")

  // Additional fields for visitor registration (only for role "user")
  nomorTelepon    String?
  tipePengunjung  VisitorType? // No default - only set for role "user"
  organizationId  String?
  organization    Organization? @relation("UserOrganization", fields: [organizationId], references: [id])

  accounts      Account[]
  sessions      Session[]
  members       Member[]
  invitations   Invitation[]
  quizResults   QuizResult[]

  @@map("user")
}

model Session {
  id             String   @id @default(cuid())
  expiresAt      DateTime
  token          String   @unique
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  ipAddress      String?
  userAgent      String?
  userId         String
  impersonatedBy String?
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  activeOrganizationId String?

  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String    @unique
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([providerId, accountId])
  @@map("account")
}

model Verification {
  id         String    @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime? @default(now())
  updatedAt  DateTime? @updatedAt

  @@map("verification")
}

model Organization {
  id          String       @id
  name        String
  slug        String?
  logo        String?
  createdAt   DateTime
  metadata    String?
  members     Member[]
  invitations Invitation[]
  customRoles CustomRole[]

  // Relations for quiz system
  users       User[]       @relation("UserOrganization")
  quizzes     Quiz[]
  videos      Video[]

  @@unique([slug])
  @@map("organization")
}

enum ModuleType {
  manajemen_pengunjung
  pengaturan_sistem
  backup_restore
  logs
  profil
  digital_permit
}

enum VisitorType {
  UMUM
  KARYAWAN
}

model Member {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String       // admin | member | custom
  customRoleId   String?      // nullable, hanya diisi jika role === "custom"
  customRole     CustomRole?  @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
  createdAt      DateTime

  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

model CustomRole {
  id              String             @id @default(cuid())
  name            String
  description     String?
  organization    Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId  String

  permissions     CustomPermission[]
  members         Member[]

  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt

  @@map("custom_role")
}

model CustomPermission {
  id             String      @id @default(cuid())
  module         ModuleType
  canRead        Boolean     @default(false)
  canCreate      Boolean     @default(false)
  canUpdate      Boolean     @default(false)
  canDelete      Boolean     @default(false)

  customRole     CustomRole  @relation(fields: [customRoleId], references: [id], onDelete: Cascade)
  customRoleId   String

  @@map("custom_permission")
}

model Video {
  id             String       @id @default(cuid())
  title          String
  youtubeUrl     String
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  visitorType    VisitorType
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@map("video")
}

model Quiz {
  id             String       @id @default(cuid())
  title          String
  description    String?
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  visitorType    VisitorType
  isActive       Boolean      @default(true)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  questions      Question[]
  quizResults    QuizResult[]

  @@map("quiz")
}

model Question {
  id             String   @id @default(cuid())
  question       String
  options        String[] // JSON array of options
  correctAnswer  Int      // Index of correct answer (0-based)
  quizId         String
  quiz           Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  order          Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("question")
}

model QuizResult {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  quizId    String
  quiz      Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)
  score     Int      // Score in percentage (0-100)
  answers   String   // JSON array of user answers
  isPassed  Boolean  @default(false)
  createdAt DateTime @default(now())

  @@map("quiz_result")
}
