import { Hono } from "hono";
import type { Context } from "hono";
import { requireAuth, requireAdmin, requireSuperAdmin, requireUser } from "../middleware/auth";
import { PrismaClient } from "@prisma/client";
import { generateId } from "better-auth";
import { OrganizationService } from "../services/organizationService";

type Variables = {
  user: any;
  session: any;
};

const prisma = new PrismaClient();

const organizationApp = new Hono<{ Variables: Variables }>()

// Add member to organization
.post("/add-member", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const { userId, organizationId, role } = await c.req.json();
    
    // Validate input
    if (!userId || !organizationId || !role) {
      return c.json({ success: false, error: "Missing required fields" }, 400);
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      return c.json({ success: false, error: "User not found" }, 404);
    }
    
    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });
    
    if (!organization) {
      return c.json({ success: false, error: "Organization not found" }, 404);
    }
    
    // Check if user is already a member
    const existingMember = await prisma.member.findFirst({
      where: {
        userId,
        organizationId
      }
    });
    
    if (existingMember) {
      // If user is already a member, update their role instead
      const updatedMember = await prisma.member.updateMany({
        where: {
          userId,
          organizationId
        },
        data: {
          role
        }
      });
      return c.json({ success: true, member: existingMember, message: "User role updated successfully" });
    }
    
    // Create member record
    const member = await prisma.member.create({
      data: {
        id: generateId(),
        userId,
        organizationId,
        role,
        createdAt: new Date()
      }
    });
    
    return c.json({ success: true, member });
  } catch (error) {
    console.error('Error adding member to organization:', error);
    return c.json({ success: false, error: "Internal server error" }, 500);
  }
})

// Update member role in organization
.post("/update-member", requireAuth, requireAdmin, requireSuperAdmin, async (c) => {
  try {
    const { userId, organizationId, role } = await c.req.json();
    
    // Validate input
    if (!userId || !organizationId || !role) {
      return c.json({ success: false, error: "Missing required fields" }, 400);
    }
    
    // Update member role
    const member = await prisma.member.updateMany({
      where: {
        userId,
        organizationId
      },
      data: {
        role
      }
    });
    
    if (member.count === 0) {
      return c.json({ success: false, error: "Member not found" }, 404);
    }
    
    return c.json({ success: true, message: "Member role updated successfully" });
  } catch (error) {
    console.error('Error updating member role:', error);
    return c.json({ success: false, error: "Internal server error" }, 500);
  }
})

// Remove member from organization
.post("/remove-member", requireAuth, requireAdmin, requireSuperAdmin, async (c) => {
  try {
    const { userId, organizationId } = await c.req.json();
    
    // Validate input
    if (!userId || !organizationId) {
      return c.json({ success: false, error: "Missing required fields" }, 400);
    }
    
    // Remove member from organization
    const result = await prisma.member.deleteMany({
      where: {
        userId,
        organizationId
      }
    });
    
    if (result.count === 0) {
      return c.json({ success: false, error: "Member not found" }, 404);
    }
    
    return c.json({ success: true, message: "Member removed from organization successfully" });
  } catch (error) {
    console.error('Error removing member from organization:', error);
    return c.json({ success: false, error: "Internal server error" }, 500);
  }
});

// Additional organization routes using separate definitions
organizationApp.get("/user/organizations", requireAuth, requireUser, async (c) => {
	try {
		const user: any = c.get("user");
		const organizations = await OrganizationService.getUserOrganizations(user.id);
		return c.json({ success: true, organizations });
	} catch (error) {
		console.error('Error fetching user organizations:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// Legacy route for frontend compatibility
organizationApp.get("/user-organizations/:userId", requireAuth, async (c) => {
	try {
		const userId = c.req.param('userId');
		const organizations = await OrganizationService.getUserOrganizations(userId);
		return c.json({ success: true, organizations });
	} catch (error) {
		console.error('Error fetching user organizations:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// Public organization route
organizationApp.get("/:slug", async (c) => {
	try {
		const slug = c.req.param('slug');
		const organization = await OrganizationService.getOrganizationBySlug(slug);
		return c.json({ success: true, organization });
	} catch (error) {
		console.error('Error fetching organization by slug:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

export default organizationApp;