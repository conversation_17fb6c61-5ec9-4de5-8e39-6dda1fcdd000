import { Hono } from "hono";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();
const app = new Hono();

/**
 * GET /api/organizations
 * Get all organizations for dropdown selection
 */
app.get("/", async (c) => {
  try {
    const organizations = await prisma.organization.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
      },
      orderBy: {
        name: 'asc'
      }
    });

    return c.json({
      success: true,
      data: organizations
    });
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return c.json(
      {
        success: false,
        error: "Failed to fetch organizations"
      },
      500
    );
  }
});

export default app;
