import { Hono } from "hono";
import { auth } from "../lib/auth";
import { requireAuth, requireRole, requireSuperAdmin, requireAdmin } from "../middleware/auth";
import { PrismaClient } from "@prisma/client";
import { generateId } from "better-auth";
import { OrganizationService } from "../services/organizationService";
import { UserService } from "../services/userService";

const prisma = new PrismaClient();

// Define context variables type
type AppVariables = {
  user: any;
  session: any;
};

const adminApp = new Hono<{ Variables: AppVariables }>();

// Get all users (superadmin only)
adminApp.get("/users", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const users = await auth.api.listUsers({
      headers: c.req.raw.headers,
      query: {},
    });

    return c.json({ users });
  } catch (error) {
    return c.json({ error: "Failed to fetch users" }, 500);
  }
});

// Get user by ID (admin and above)
adminApp.get("/users/:id", requireAuth, requireAdmin, async (c) => {
  try {
    const userId = c.req.param("id");
    // For now, return a placeholder since getUser might not be available
    // In a real implementation, you would query your database directly
    return c.json({ message: "Get user by ID - implementation needed", userId });
  } catch (error) {
    return c.json({ error: "Failed to fetch user" }, 500);
  }
});

// Update user role (superadmin only)
adminApp.patch("/users/:id/role", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const userId = c.req.param("id");
    const { role } = await c.req.json();

    if (!role || !["user", "admin", "superadmin"].includes(role)) {
      return c.json({ error: "Invalid role" }, 400);
    }

    const updatedUser = await auth.api.setRole({
      headers: c.req.raw.headers,
      body: { userId, role },
    });

    return c.json({ user: updatedUser });
  } catch (error) {
    return c.json({ error: "Failed to update user role" }, 500);
  }
});

// Ban user (admin and above)
adminApp.patch("/users/:id/ban", requireAuth, requireAdmin, async (c) => {
  try {
    const userId = c.req.param("id");
    const { banExpiresIn, banReason } = await c.req.json();

    const result = await auth.api.banUser({
      headers: c.req.raw.headers,
      body: {
        userId,
        banExpiresIn,
        banReason,
      },
    });

    return c.json({ success: true, result });
  } catch (error) {
    return c.json({ error: "Failed to ban user" }, 500);
  }
});

// Unban user (admin and above)
adminApp.patch("/users/:id/unban", requireAuth, requireAdmin, async (c) => {
  try {
    const userId = c.req.param("id");

    const result = await auth.api.unbanUser({
      headers: c.req.raw.headers,
      body: { userId },
    });

    return c.json({ success: true, result });
  } catch (error) {
    return c.json({ error: "Failed to unban user" }, 500);
  }
});

// Impersonate user (superadmin only)
adminApp.post("/users/:id/impersonate", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const userId = c.req.param("id");

    const result = await auth.api.impersonateUser({
      headers: c.req.raw.headers,
      body: { userId },
    });

    return c.json({ success: true, result });
  } catch (error) {
    return c.json({ error: "Failed to impersonate user" }, 500);
  }
});

// Stop impersonation (superadmin only)
adminApp.post("/impersonate/stop", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const result = await auth.api.stopImpersonating({
      headers: c.req.raw.headers,
    });

    return c.json({ success: true, result });
  } catch (error) {
    return c.json({ error: "Failed to stop impersonation" }, 500);
  }
});

// Update user data (admin and above)
adminApp.post("/update-user", requireAuth, requireAdmin, async (c) => {
  try {
    const { userId, name, email } = await c.req.json();
    
    console.log('🔍 Update user data request:', { userId, name, email });
    
    if (!userId) {
      return c.json({ success: false, error: 'User ID is required' }, 400);
    }

    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;

    // Update user data in database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData
    });
    
    console.log('✅ User data updated:', { userId, updatedFields: Object.keys(updateData) });

    return c.json({ 
      success: true, 
      user: updatedUser,
      message: 'User data updated successfully'
    });
  } catch (error: any) {
    console.error('❌ Error updating user data:', error);
    if (error.code === 'P2002') {
      return c.json({ success: false, error: 'Email already exists' }, 400);
    }
    return c.json({ success: false, error: 'Failed to update user data' }, 500);
  }
});

// Update user role with organization management (admin and above)
adminApp.post("/update-user-role", requireAuth, requireAdmin, async (c) => {
  try {
    const { userId, role, organizationId } = await c.req.json();
    
    console.log('🔍 Update user role request:', { userId, role, organizationId });
    
    if (!userId || !role) {
      return c.json({ success: false, error: 'User ID and role are required' }, 400);
    }

    // Update user role using better-auth API
    const updatedUser = await auth.api.setRole({
      headers: c.req.raw.headers,
      body: { userId, role },
    });
    
    console.log('✅ User role updated via better-auth:', { userId, newRole: role });

    // Handle organization membership based on role
    console.log('🔍 Checking organization membership conditions:', { 
      role, 
      organizationId,
      isSuperadmin: role === 'superadmin'
    });

    if (role === 'superadmin') {
      // Superadmin should not be a member of any organization
      console.log('🔄 Removing superadmin from all organizations...');
      await prisma.member.deleteMany({
        where: { userId }
      });
      console.log('✅ Superadmin removed from all organizations');
    } else if (organizationId && ['admin', 'user'].includes(role)) {
      // For admin/user roles, ensure they are a member of the specified organization
      console.log('🔍 Checking if user is member of organization:', organizationId);
      
      const existingMember = await prisma.member.findFirst({
        where: {
          userId,
          organizationId
        }
      });
      
      if (!existingMember) {
        // Map user role to organization member role
        const memberRole = role === 'user' ? 'member' : role; // user -> member, admin -> admin
        console.log(`➕ Adding user to organization with role: ${memberRole}...`);
        await prisma.member.create({
          data: {
            id: generateId(),
            userId,
            organizationId,
            role: memberRole,
            createdAt: new Date()
          }
        });
        console.log('✅ User added to organization');
      } else {
        // Map user role to organization member role
        const memberRole = role === 'user' ? 'member' : role; // user -> member, admin -> admin
        console.log(`🔄 Updating existing member role from ${existingMember.role} to ${memberRole}...`);
        await prisma.member.update({
          where: { id: existingMember.id },
          data: { role: memberRole }
        });
        console.log('✅ Member role updated to match user role');
      }
    }

    return c.json({ 
      success: true, 
      user: updatedUser,
      message: `User role updated to ${role} successfully`
    });
  } catch (error: any) {
    console.error('❌ Error updating user role:', error);
    return c.json({ success: false, error: 'Failed to update user role' }, 500);
  }
});

// Create user with organization assignment (admin and above)
adminApp.post("/create-user-with-org", requireAuth, requireAdmin, async (c) => {
  try {
    const { email, password, name, role, organizationId } = await c.req.json();
    
    console.log('🔍 Creating user with organization:', { email, name, role, organizationId });
    
    if (!email || !password || !name || !role) {
      return c.json({ success: false, error: 'Email, password, name, and role are required' }, 400);
    }

    if (!['admin', 'user'].includes(role)) {
      return c.json({ success: false, error: 'Invalid role for organization assignment' }, 400);
    }

    if (!organizationId) {
      return c.json({ success: false, error: 'Organization ID is required for admin/user roles' }, 400);
    }

    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });
    if (!organization) {
      return c.json({ success: false, error: 'Organization not found' }, 404);
    }

    // Create user using better-auth
    const result = await auth.api.createUser({
      headers: c.req.raw.headers,
      body: {
        email,
        password,
        name,
        role,
      },
    });

    console.log('🔍 Create user result:', { result, hasData: !!result.data, hasError: !!result.error });

    if (result.error) {
      console.error('❌ Create user error:', result.error);
      return c.json({ success: false, error: result.error.message || 'Failed to create user' }, 400);
    }

    // Better-auth returns user data in result.user property
    const newUser = result.user;
    
    if (!newUser || !newUser.id) {
      console.error('❌ Invalid user data structure:', newUser);
      return c.json({ success: false, error: 'Failed to create user - invalid response structure' }, 500);
    }

    console.log('✅ User created successfully:', { userId: newUser.id, email: newUser.email });

    // Add user to organization
    try {
      // Map user role to organization member role
      const memberRole = role === 'user' ? 'member' : role; // user -> member, admin -> admin
      await prisma.member.create({
        data: {
          id: generateId(),
          userId: newUser.id,
          organizationId,
          role: memberRole,
          createdAt: new Date()
        }
      });
      console.log('✅ User added to organization:', { userId: newUser.id, organizationId, memberRole });
    } catch (orgError) {
      console.error('❌ Error adding user to organization:', orgError);
      // User was created but failed to add to organization
      return c.json({ 
        success: false, 
        error: 'User created but failed to add to organization',
        userId: newUser.id 
      }, 500);
    }

    return c.json({ 
      success: true, 
      data: newUser,
      message: `User created and added to organization successfully`
    });
  } catch (error: any) {
    console.error('❌ Error creating user with organization:', error);
    if (error.code === 'P2002') {
      return c.json({ success: false, error: 'Email already exists' }, 400);
    }
    return c.json({ success: false, error: 'Failed to create user with organization' }, 500);
  }
});

// Get organization members (admin and above)
adminApp.get("/organization-members/:organizationId", requireAuth, requireAdmin, async (c) => {
  try {
    const organizationId = c.req.param('organizationId');
    const user = c.get('user') as any;
    
    if (!organizationId) {
      return c.json({ success: false, error: 'Organization ID is required' }, 400);
    }

    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });
    if (!organization) {
      return c.json({ success: false, error: 'Organization not found' }, 404);
    }

    // For non-superadmin users, check if they are a member of the requested organization
    if (user.role !== 'superadmin') {
      const userMembership = await prisma.member.findFirst({
        where: {
          userId: user.id,
          organizationId
        }
      });
      if (!userMembership) {
        return c.json({ success: false, error: 'Access denied: You can only view members of your own organization' }, 403);
      }
    }

    // Get users yang tergabung dalam organisasi ini dengan session data untuk lastActive
    const users = await prisma.user.findMany({
      where: {
        members: {
          some: {
            organizationId: organization.id
          }
        }
      },
      include: {
        members: {
          where: {
            organizationId: organization.id
          },
          select: {
            id: true,
            role: true,
            createdAt: true
          }
        },
        sessions: {
          orderBy: {
            updatedAt: 'desc'
          },
          take: 1,
          select: {
            updatedAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Found ${users.length} users for organization ${organization.name}`);
    
    // Debug: Log session data untuk setiap user
    users.forEach((user, index) => {
      const lastSession = user.sessions[0];
      console.log(`🔍 User ${index + 1} session data:`, {
        userId: user.id,
        name: user.name,
        hasSession: !!lastSession,
        lastSessionUpdate: lastSession?.updatedAt,
        userUpdatedAt: user.updatedAt,
        userCreatedAt: user.createdAt
      });
    });

    // Transform data - menggunakan data langsung dari tabel User
    const organizationMembers = users
      .filter(user => user.members.length > 0)
      .map(user => {
        const memberData = user.members[0]; // Ambil data member pertama (seharusnya hanya ada 1)
        if (!memberData) {
          throw new Error(`Member data not found for user ${user.id}`);
        }
        return {
          id: user.id, // Menggunakan user ID sebagai identifier
          name: user.name || 'Nama tidak tersedia',
          email: user.email || 'Email tidak tersedia', 
          role: memberData.role, // Role dari table member
          systemRole: user.role, // System role dari table user
          memberId: memberData.id, // Member ID untuk referensi
          joinedAt: new Date(memberData.createdAt).toLocaleString('id-ID', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          }),
          lastActive: (() => {
            // Gunakan session terakhir jika ada, jika tidak gunakan updatedAt user, jika tidak ada gunakan createdAt
            const lastSessionUpdate = user.sessions[0]?.updatedAt;
            const lastActiveDate = lastSessionUpdate || user.updatedAt || user.createdAt;
            return new Date(lastActiveDate).toLocaleString('id-ID', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            });
          })()
        };
      });
    
    return c.json({ success: true, members: organizationMembers });
  } catch (error) {
    console.error('Error fetching organization members:', error);
    return c.json({ success: false, error: "Failed to fetch organization members" }, 500);
  }
});

// Get all organizations (superadmin only)
adminApp.get("/all-organizations", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const organizations = await prisma.organization.findMany({
      include: {
        _count: {
          select: {
            members: true
          }
        }
      }
    });
    
    const formattedOrganizations = organizations.map((org: any) => ({
      id: org.id,
      name: org.name,
      slug: org.slug,
      logo: org.logo,
      metadata: org.metadata ? JSON.parse(org.metadata) : null,
      memberCount: org._count.members,
      createdAt: org.createdAt // Send as ISO string for proper parsing
    }));
    
    return c.json({ success: true, organizations: formattedOrganizations });
  } catch (error) {
    console.error('Error fetching organizations:', error);
    return c.json({ success: false, error: "Failed to fetch organizations" }, 500);
  }
});

// Legacy admin routes (for backward compatibility)
adminApp.post("/create-organization", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { name, slug, logo, metadata } = await c.req.json();
		const organization = await OrganizationService.createOrganization(name, slug, logo, metadata);
		return c.json({ 
			success: true, 
			organization,
			message: "Organization created successfully. Superadmin is not a member." 
		});
	} catch (error) {
		console.error('Error creating organization:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

adminApp.get("/all-organizations", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const organizations = await OrganizationService.getAllOrganizations();
		return c.json({ success: true, organizations });
	} catch (error) {
		console.error('Error fetching all organizations:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

adminApp.get("/users-with-organizations", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const result = await UserService.getAllUsers();
		return c.json({ success: true, users: result.users });
	} catch (error) {
		console.error('Error fetching users with organizations:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// UI-only routes (no implementation)
adminApp.get("/organizations", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ organizations: [], message: "UI only - no implementation" });
});

adminApp.get("/members", requireAuth, requireAdmin, async (c) => {
	return c.json({ members: [], message: "UI only - no implementation" });
});

adminApp.post("/organizations", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ success: true, message: "UI only - no implementation" });
});

adminApp.put("/organizations/:id", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const organizationId = c.req.param('id');
		const { name, slug, logo, metadata } = await c.req.json();
		const organization = await OrganizationService.updateOrganization(organizationId, name, slug, logo, metadata);
		return c.json({ 
			success: true, 
			organization,
			message: "Organization updated successfully" 
		});
	} catch (error) {
		console.error('Error updating organization:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

adminApp.delete("/organizations/:id", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const organizationId = c.req.param('id');
		const result = await OrganizationService.deleteOrganization(organizationId);
		return c.json({ 
			success: true, 
			message: result.message 
		});
	} catch (error) {
		console.error('Error deleting organization:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// User management routes
adminApp.post("/update-user-role", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { userId, role } = await c.req.json();
		const result = await UserService.updateUserRole(userId, role);
		return c.json({ success: true, user: result });
	} catch (error) {
		console.error('Error updating user role:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

adminApp.post("/create-user-with-organization", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const userData = await c.req.json();
		const user = await UserService.createUserWithOrganization(userData);
		return c.json({ success: true, user });
	} catch (error) {
		console.error('Error creating user with organization:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// Get organization by slug (admin and above)
adminApp.get("/organization-by-slug/:slug", requireAuth, requireAdmin, async (c) => {
	try {
		const slug = c.req.param('slug');
		const organization = await OrganizationService.getOrganizationBySlug(slug);
		return c.json({ success: true, data: organization });
	} catch (error) {
		console.error('Error getting organization by slug:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

// Role Management endpoints
// Get unique roles with priority: Member role > User role
adminApp.get("/unique-roles", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { page = 1, limit = 10, search = '' } = c.req.query();
		const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
		
		// Get all users with their member relationships
		const users = await prisma.user.findMany({
			include: {
				members: {
					include: {
						organization: true,
						customRole: true
					}
				}
			},
			orderBy: { createdAt: 'desc' }
		});
		
		// Process users to get unique roles with priority
		const uniqueRoles = users.map(user => {
			// If user has member relationships, use the first one (priority to member role)
			if (user.members.length > 0) {
				const member = user.members[0]; // Take first membership
				if (member) {
					return {
						id: member.id,
						role: member.customRole ? 'custom' : member.role,
						customRoleName: member.customRole?.name,
						organizationName: member.organization.name,
						organizationId: member.organizationId,
						userId: user.id,
						userName: user.name,
						userEmail: user.email,
						createdAt: member.createdAt,
						type: 'member'
					};
				}
			}
			// User without member relationship, use user role
			return {
				id: user.id,
				role: user.role,
				customRoleName: null,
				organizationName: '-',
				organizationId: null,
				userId: user.id,
				userName: user.name,
				userEmail: user.email,
				createdAt: user.createdAt,
				type: 'user'
			};
		});
		
		// Apply search filter if provided
		const filteredRoles = search ? uniqueRoles.filter(role => 
			role.role.toLowerCase().includes(search.toLowerCase()) ||
			role.organizationName.toLowerCase().includes(search.toLowerCase()) ||
			role.userName?.toLowerCase().includes(search.toLowerCase()) ||
			role.userEmail?.toLowerCase().includes(search.toLowerCase())
		) : uniqueRoles;
		
		// Apply pagination
		const paginatedRoles = filteredRoles.slice(skip, skip + parseInt(limit as string));
		
		return c.json({ 
			success: true, 
			roles: paginatedRoles,
			total: filteredRoles.length,
			page: parseInt(page as string),
			limit: parseInt(limit as string)
		});
	} catch (error) {
		console.error('Error fetching unique roles:', error);
		return c.json({ success: false, error: "Failed to fetch unique roles" }, 500);
	}
});

// Get all roles (members with user and organization details) - DEPRECATED
adminApp.get("/roles", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { page = 1, limit = 10, search = '' } = c.req.query();
		const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
		
		const whereClause = search ? {
			OR: [
				{ user: { name: { contains: search as string, mode: 'insensitive' as const } } },
				{ user: { email: { contains: search as string, mode: 'insensitive' as const } } },
				{ organization: { name: { contains: search as string, mode: 'insensitive' as const } } },
				{ role: { contains: search as string, mode: 'insensitive' as const } }
			]
		} : {};
		
		const [members, total] = await Promise.all([
			prisma.member.findMany({
				where: whereClause,
				include: {
					user: true,
					organization: true
				},
				skip,
				take: parseInt(limit as string),
				orderBy: { createdAt: 'desc' }
			}),
			prisma.member.count({ where: whereClause })
		]);
		
		const roles = members.map(member => ({
			id: member.id,
			role: member.role,
			user: {
				id: member.user.id,
				name: member.user.name || 'Unknown',
				email: member.user.email || 'No email'
			},
			organization: {
				id: member.organization.id,
				name: member.organization.name
			},
			createdAt: member.createdAt
		}));
		
		return c.json({ 
			success: true, 
			roles,
			total,
			page: parseInt(page as string),
			limit: parseInt(limit as string)
		});
	} catch (error) {
		console.error('Error fetching roles:', error);
		return c.json({ success: false, error: "Failed to fetch roles" }, 500);
	}
});

// Create new role assignment
adminApp.post("/roles", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { userId, organizationId, role } = await c.req.json();
		
		// Check if user already has a role in this organization
		const existingMember = await prisma.member.findFirst({
			where: {
				userId,
				organizationId
			}
		});
		
		if (existingMember) {
			return c.json({ 
				success: false, 
				error: "User already has a role in this organization" 
			}, 400);
		}
		
		const member = await prisma.member.create({
			data: {
				id: `member_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				userId,
				organizationId,
				role,
				createdAt: new Date()
			},
			include: {
				user: true,
				organization: true
			}
		});
		
		const roleData = {
			id: member.id,
			role: member.role,
			user: {
				id: member.user.id,
				name: member.user.name || 'Unknown'
			},
			organization: {
				id: member.organization.id,
				name: member.organization.name
			},
			createdAt: member.createdAt
		};
		
		return c.json({ success: true, role: roleData });
	} catch (error) {
		console.error('Error creating role:', error);
		return c.json({ success: false, error: "Failed to create role" }, 500);
	}
});

// Update role assignment
adminApp.put("/roles/:id", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const roleId = c.req.param('id');
		const { role } = await c.req.json();
		
		const member = await prisma.member.update({
			where: { id: roleId },
			data: { role },
			include: {
				user: true,
				organization: true
			}
		});
		
		const roleData = {
			id: member.id,
			role: member.role,
			user: {
				id: member.user.id,
				name: member.user.name || 'Unknown'
			},
			organization: {
				id: member.organization.id,
				name: member.organization.name
			},
			createdAt: member.createdAt
		};
		
		return c.json({ success: true, role: roleData });
	} catch (error) {
		console.error('Error updating role:', error);
		return c.json({ success: false, error: "Failed to update role" }, 500);
	}
});

// Delete role assignment
adminApp.delete("/roles/:id", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const roleId = c.req.param('id');
		
		await prisma.member.delete({
			where: { id: roleId }
		});
		
		return c.json({ success: true, message: "Role deleted successfully" });
	} catch (error) {
		console.error('Error deleting role:', error);
		return c.json({ success: false, error: "Failed to delete role" }, 500);
	}
});

// Get available users (users without role in any organization)
adminApp.get("/available-users", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const users = await prisma.user.findMany({
			where: {
				members: {
					none: {}
				}
			},
			select: {
				id: true,
				name: true,
				email: true
			},
			orderBy: { name: 'asc' }
		});
		
		return c.json({ success: true, users });
	} catch (error) {
		console.error('Error fetching available users:', error);
		return c.json({ success: false, error: "Failed to fetch available users" }, 500);
	}
});

// Get available organizations
adminApp.get("/available-organizations", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const organizations = await prisma.organization.findMany({
			select: {
				id: true,
				name: true
			},
			orderBy: { name: 'asc' }
		});
		
		return c.json({ success: true, organizations });
	} catch (error) {
		console.error('Error fetching available organizations:', error);
		return c.json({ success: false, error: "Failed to fetch available organizations" }, 500);
	}
});

// Custom Role Management Endpoints

// Get all custom roles (superadmin only)
adminApp.get("/custom-roles", requireAuth, requireSuperAdmin, async (c) => {
	try {
		const { page = 1, limit = 10, search = '' } = c.req.query();
		const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
		
		const whereClause = search ? {
			OR: [
				{ name: { contains: search as string, mode: 'insensitive' as const } },
				{ description: { contains: search as string, mode: 'insensitive' as const } },
				{ organization: { name: { contains: search as string, mode: 'insensitive' as const } } }
			]
		} : {};
		
		const customRoles = await prisma.customRole.findMany({
			where: whereClause,
			include: {
				organization: {
					select: {
						id: true,
						name: true
					}
				},
				permissions: true,
				members: {
					include: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				}
			},
			orderBy: { createdAt: 'desc' },
			skip,
			take: parseInt(limit as string)
		});
		
		const total = await prisma.customRole.count({ where: whereClause });
		
		return c.json({ 
			success: true, 
			customRoles,
			total,
			page: parseInt(page as string),
			limit: parseInt(limit as string),
			totalPages: Math.ceil(total / parseInt(limit as string))
		});
	} catch (error) {
		console.error('Error fetching all custom roles:', error);
		return c.json({ success: false, error: "Failed to fetch custom roles" }, 500);
	}
});

// Get single custom role by ID
adminApp.get("/custom-roles/:id", requireAuth, requireAdmin, async (c) => {
	try {
		const roleId = c.req.param('id');
		const user = c.get('user') as any;
		
		const customRole = await prisma.customRole.findUnique({
			where: { id: roleId },
			include: {
				organization: {
					select: {
						id: true,
						name: true
					}
				},
				permissions: true,
				members: {
					include: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				}
			}
		});
		
		if (!customRole) {
			return c.json({ success: false, error: 'Custom role not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: customRole.organizationId
				}
			});
			if (!userMembership) {
				return c.json({ success: false, error: 'Access denied: You can only view custom roles of your own organization' }, 403);
			}
		}
		
		return c.json({ success: true, customRole });
	} catch (error) {
		console.error('Error fetching custom role:', error);
		return c.json({ success: false, error: "Failed to fetch custom role" }, 500);
	}
});

// Get custom roles for an organization
adminApp.get("/custom-roles/organization/:organizationId", requireAuth, requireAdmin, async (c) => {
	try {
		const organizationId = c.req.param('organizationId');
		const user = c.get('user') as any;
		
		// Check if organization exists
		const organization = await prisma.organization.findUnique({
			where: { id: organizationId }
		});
		if (!organization) {
			return c.json({ success: false, error: 'Organization not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the requested organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId
				}
			});
			if (!userMembership) {
				return c.json({ success: false, error: 'Access denied: You can only view custom roles of your own organization' }, 403);
			}
		}
		
		const customRoles = await prisma.customRole.findMany({
			where: { organizationId },
			include: {
				permissions: true,
				members: {
					include: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				}
			},
			orderBy: { createdAt: 'desc' }
		});
		
		return c.json({ success: true, customRoles });
	} catch (error) {
		console.error('Error fetching custom roles:', error);
		return c.json({ success: false, error: "Failed to fetch custom roles" }, 500);
	}
});

// Create custom role
adminApp.post("/custom-roles", requireAuth, requireAdmin, async (c) => {
	try {
		const { name, description, organizationId, permissions } = await c.req.json();
		const user = c.get('user') as any;
		
		if (!name || !organizationId || !permissions) {
			return c.json({ success: false, error: 'Name, organization ID, and permissions are required' }, 400);
		}
		
		// Check if organization exists
		const organization = await prisma.organization.findUnique({
			where: { id: organizationId }
		});
		if (!organization) {
			return c.json({ success: false, error: 'Organization not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the requested organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId
				}
			});
			if (!userMembership || userMembership.role !== 'admin') {
				return c.json({ success: false, error: 'Access denied: Only organization admins can create custom roles' }, 403);
			}
		}
		
		// Check if custom role name already exists in this organization
		const existingRole = await prisma.customRole.findFirst({
			where: {
				name,
				organizationId
			}
		});
		if (existingRole) {
			return c.json({ success: false, error: 'Custom role with this name already exists in this organization' }, 400);
		}
		
		const customRole = await prisma.customRole.create({
			data: {
				name,
				description,
				organizationId,
				permissions: {
					create: permissions.map((permission: any) => ({
						module: permission.moduleType,
						canCreate: permission.canCreate || false,
						canRead: permission.canRead || false,
						canUpdate: permission.canUpdate || false,
						canDelete: permission.canDelete || false
					}))
				}
			},
			include: {
				permissions: true
			}
		});
		
		return c.json({ success: true, customRole });
	} catch (error) {
		console.error('Error creating custom role:', error);
		return c.json({ success: false, error: "Failed to create custom role" }, 500);
	}
});

// Update custom role
adminApp.put("/custom-roles/:id", requireAuth, requireAdmin, async (c) => {
	try {
		const roleId = c.req.param('id');
		const { name, description, permissions } = await c.req.json();
		const user = c.get('user') as any;
		
		// Check if custom role exists
		const existingRole = await prisma.customRole.findUnique({
			where: { id: roleId },
			include: { organization: true }
		});
		if (!existingRole) {
			return c.json({ success: false, error: 'Custom role not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: existingRole.organizationId
				}
			});
			if (!userMembership || userMembership.role !== 'admin') {
				return c.json({ success: false, error: 'Access denied: Only organization admins can update custom roles' }, 403);
			}
		}
		
		// Delete existing permissions
		await prisma.customPermission.deleteMany({
			where: { customRoleId: roleId }
		});
		
		// Update custom role with new permissions
		const updatedRole = await prisma.customRole.update({
			where: { id: roleId },
			data: {
				name,
				description,
				permissions: {
					create: permissions.map((permission: any) => ({
						module: permission.moduleType,
						canCreate: permission.canCreate || false,
						canRead: permission.canRead || false,
						canUpdate: permission.canUpdate || false,
						canDelete: permission.canDelete || false
					}))
				}
			},
			include: {
				permissions: true
			}
		});
		
		return c.json({ success: true, customRole: updatedRole });
	} catch (error) {
		console.error('Error updating custom role:', error);
		return c.json({ success: false, error: "Failed to update custom role" }, 500);
	}
});

// Delete custom role
adminApp.delete("/custom-roles/:id", requireAuth, requireAdmin, async (c) => {
	try {
		const roleId = c.req.param('id');
		const user = c.get('user') as any;
		
		// Check if custom role exists
		const existingRole = await prisma.customRole.findUnique({
			where: { id: roleId }
		});
		if (!existingRole) {
			return c.json({ success: false, error: 'Custom role not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: existingRole.organizationId
				}
			});
			if (!userMembership || userMembership.role !== 'admin') {
				return c.json({ success: false, error: 'Access denied: Only organization admins can delete custom roles' }, 403);
			}
		}
		
		// Check if any members are using this custom role
		const membersUsingRole = await prisma.member.count({
			where: { customRoleId: roleId }
		});
		if (membersUsingRole > 0) {
			return c.json({ success: false, error: 'Cannot delete custom role: It is currently assigned to members' }, 400);
		}
		
		// Delete custom role (permissions will be deleted automatically due to cascade)
		await prisma.customRole.delete({
			where: { id: roleId }
		});
		
		return c.json({ success: true, message: "Custom role deleted successfully" });
	} catch (error) {
		console.error('Error deleting custom role:', error);
		return c.json({ success: false, error: "Failed to delete custom role" }, 500);
	}
});

// Assign custom role to member
adminApp.post("/assign-custom-role", requireAuth, requireAdmin, async (c) => {
	try {
		const { memberId, customRoleId } = await c.req.json();
		const user = c.get('user') as any;
		
		if (!memberId || !customRoleId) {
			return c.json({ success: false, error: 'Member ID and custom role ID are required' }, 400);
		}
		
		// Check if member exists
		const member = await prisma.member.findUnique({
			where: { id: memberId }
		});
		if (!member) {
			return c.json({ success: false, error: 'Member not found' }, 404);
		}
		
		// Check if custom role exists
		const customRole = await prisma.customRole.findUnique({
			where: { id: customRoleId }
		});
		if (!customRole) {
			return c.json({ success: false, error: 'Custom role not found' }, 404);
		}
		
		// For non-superadmin users, check if they are a member of the organization
		if (user.role !== 'superadmin') {
			const userMembership = await prisma.member.findFirst({
				where: {
					userId: user.id,
					organizationId: member.organizationId
				}
			});
			if (!userMembership || userMembership.role !== 'admin') {
				return c.json({ success: false, error: 'Access denied: Only organization admins can assign custom roles' }, 403);
			}
		}
		
		// Update member with custom role
		const updatedMember = await prisma.member.update({
			where: { id: memberId },
			data: { customRoleId },
			include: {
				user: true,
				organization: true,
				customRole: {
					include: {
						permissions: true
					}
				}
			}
		});
		
		return c.json({ success: true, member: updatedMember });
	} catch (error) {
		console.error('Error assigning custom role:', error);
		return c.json({ success: false, error: "Failed to assign custom role" }, 500);
	}
});

// Get user permissions
adminApp.get("/user-permissions/:userId", requireAuth, async (c) => {
	try {
		const userId = c.req.param('userId');
		const currentUser = c.get('user') as any;
		
		// Users can only check their own permissions unless they are superadmin
		if (currentUser.role !== 'superadmin' && currentUser.id !== userId) {
			return c.json({ success: false, error: 'Access denied: You can only check your own permissions' }, 403);
		}
		
		// Get user with their memberships and custom roles
		const user = await prisma.user.findUnique({
			where: { id: userId },
			include: {
				members: {
					include: {
						organization: true,
						customRole: {
							include: {
								permissions: true
							}
						}
					}
				}
			}
		});
		
		if (!user) {
			return c.json({ success: false, error: 'User not found' }, 404);
		}
		
		// Build permissions object
		const permissions: any = {
			global: {
				role: user.role,
				isSuperAdmin: user.role === 'superadmin'
			},
			organizations: {}
		};
		
		// Add organization-specific permissions
		user.members.forEach(member => {
			const orgId = member.organizationId;
			permissions.organizations[orgId] = {
				organization: {
					id: member.organization.id,
					name: member.organization.name
				},
				role: member.role,
				customRole: member.customRole ? {
					id: member.customRole.id,
					name: member.customRole.name,
					description: member.customRole.description
				} : null,
				permissions: {}
			};
			
			// Add custom permissions if available
			if (member.customRole && member.customRole.permissions) {
				member.customRole.permissions.forEach(permission => {
					permissions.organizations[orgId].permissions[permission.module] = {
						canCreate: permission.canCreate,
						canRead: permission.canRead,
						canUpdate: permission.canUpdate,
						canDelete: permission.canDelete
					};
				});
			} else {
				// Default permissions based on role
				const moduleTypes = ['manajemen_pengunjung', 'pengaturan_sistem', 'backup_restore', 'logs', 'profil', 'digital_permit'];
				moduleTypes.forEach(moduleType => {
					if (member.role === 'admin') {
						permissions.organizations[orgId].permissions[moduleType] = {
							canCreate: true,
							canRead: true,
							canUpdate: true,
							canDelete: true
						};
					} else if (member.role === 'member') {
						permissions.organizations[orgId].permissions[moduleType] = {
							canCreate: false,
							canRead: true,
							canUpdate: false,
							canDelete: false
						};
					}
				});
			}
		});
		
		return c.json({ success: true, permissions });
	} catch (error) {
		console.error('Error fetching user permissions:', error);
		return c.json({ success: false, error: "Failed to fetch user permissions" }, 500);
	}
});

// Check specific permission
adminApp.post("/check-permission", requireAuth, async (c) => {
	try {
		const { userId, organizationId, moduleType, action } = await c.req.json();
		const currentUser = c.get('user') as any;
		
		// Users can only check their own permissions unless they are superadmin
		if (currentUser.role !== 'superadmin' && currentUser.id !== userId) {
			return c.json({ success: false, error: 'Access denied: You can only check your own permissions' }, 403);
		}
		
		if (!userId || !organizationId || !moduleType || !action) {
			return c.json({ success: false, error: 'User ID, organization ID, module type, and action are required' }, 400);
		}
		
		// Check if user is superadmin
		const user = await prisma.user.findUnique({
			where: { id: userId }
		});
		if (!user) {
			return c.json({ success: false, error: 'User not found' }, 404);
		}
		
		if (user.role === 'superadmin') {
			return c.json({ success: true, hasAccess: true, reason: 'Superadmin has access to all modules' });
		}
		
		// Get user membership in the organization
		const member = await prisma.member.findFirst({
			where: {
				userId,
				organizationId
			},
			include: {
				customRole: {
					include: {
						permissions: true
					}
				}
			}
		});
		
		if (!member) {
			return c.json({ success: true, hasAccess: false, reason: 'User is not a member of this organization' });
		}
		
		// Check custom role permissions first
		if (member.customRole && member.customRole.permissions) {
			const permission = member.customRole.permissions.find(p => p.module === moduleType);
			if (permission) {
				const hasAccess = permission[`can${action.charAt(0).toUpperCase() + action.slice(1)}` as keyof typeof permission] as boolean;
				return c.json({ 
					success: true, 
					hasAccess, 
					reason: hasAccess ? `Custom role '${member.customRole.name}' grants ${action} access to ${moduleType}` : `Custom role '${member.customRole.name}' denies ${action} access to ${moduleType}`
				});
			}
		}
		
		// Fall back to default role permissions
		let hasAccess = false;
		let reason = '';
		
		if (member.role === 'admin') {
			hasAccess = true;
			reason = `Admin role grants ${action} access to ${moduleType}`;
		} else if (member.role === 'member') {
			hasAccess = action === 'read';
			reason = hasAccess ? `Member role grants read access to ${moduleType}` : `Member role denies ${action} access to ${moduleType}`;
		}
		
		return c.json({ success: true, hasAccess, reason });
	} catch (error) {
		console.error('Error checking permission:', error);
		return c.json({ success: false, error: "Failed to check permission" }, 500);
	}
});

// Update user with custom role
adminApp.post("/update-user-with-custom-role", requireAuth, requireAdmin, async (c) => {
	try {
		const { userId, customRoleId, name, email } = await c.req.json();
		
		if (!userId || !customRoleId) {
			return c.json({ success: false, error: 'User ID and custom role ID are required' }, 400);
		}
		
		// Check if custom role exists
		const customRole = await prisma.customRole.findUnique({
			where: { id: customRoleId }
		});
		
		if (!customRole) {
			return c.json({ success: false, error: 'Custom role not found' }, 404);
		}
		
		// Update user basic info if provided
		const updateData: any = {};
		if (name) updateData.name = name;
		if (email) updateData.email = email;
		
		if (Object.keys(updateData).length > 0) {
			await prisma.user.update({
				where: { id: userId },
				data: updateData
			});
		}
		
		// Find user's membership in the custom role's organization
		const existingMember = await prisma.member.findFirst({
			where: {
				userId,
				organizationId: customRole.organizationId
			}
		});
		
		if (existingMember) {
			// Update existing membership with custom role
			await prisma.member.update({
				where: { id: existingMember.id },
				data: { customRoleId }
			});
		} else {
			// Create new membership with custom role
			await prisma.member.create({
				data: {
					id: generateId(),
					userId,
					organizationId: customRole.organizationId,
					role: 'member', // Default role for custom role users
					customRoleId,
					createdAt: new Date()
				}
			});
		}
		
		// Get updated user with organization info
		const updatedUser = await prisma.user.findUnique({
			where: { id: userId },
			include: {
				members: {
					include: {
						organization: true,
						customRole: true
					}
				}
			}
		});
		
		return c.json({ success: true, data: updatedUser });
	} catch (error: any) {
		console.error('Error updating user with custom role:', error);
		if (error.code === 'P2002') {
			return c.json({ success: false, error: 'Email already exists' }, 400);
		} else {
			return c.json({ success: false, error: 'Failed to update user with custom role' }, 500);
		}
	}
});

export default adminApp;