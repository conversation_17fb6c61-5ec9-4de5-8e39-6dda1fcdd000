import { Hono } from "hono";
import { PrismaClient } from "@prisma/client";
import { requireAuth } from "../middleware/auth";

const prisma = new PrismaClient();
const app = new Hono();

/**
 * GET /api/user/profile
 * Get current user profile with additional fields
 */
app.get("/profile", requireAuth, async (c) => {
  try {
    const user: any = (c as any).get("user");
    
    // Get complete user data from database
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    if (!userData) {
      return c.json({
        success: false,
        error: "User not found"
      }, 404);
    }

    return c.json({
      success: true,
      data: {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        nomorTelepon: userData.nomorTelepon,
        tipePengunjung: userData.tipePengunjung,
        organizationId: userData.organizationId,
        organization: userData.organization,
        emailVerified: userData.emailVerified,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt
      }
    });
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return c.json({
      success: false,
      error: "Failed to fetch user profile"
    }, 500);
  }
});

export default app;
