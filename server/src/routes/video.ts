import { Hono } from "hono";
import { PrismaClient } from "@prisma/client";
import { requireAuth, requireRole } from "../middleware/auth";

const prisma = new PrismaClient();
const app = new Hono();

/**
 * GET /api/video/organizations/:orgId/videos
 * Get all videos for an organization
 */
app.get("/organizations/:orgId/videos", requireAuth, async (c) => {
  try {
    const orgId = c.req.param("orgId");
    const visitorType = c.req.query("visitorType"); // Optional filter

    const whereClause: any = {
      organizationId: orgId
    };

    if (visitorType && (visitorType === 'UMUM' || visitorType === 'KARYAWAN')) {
      whereClause.visitorType = visitorType;
    }

    const videos = await prisma.video.findMany({
      where: whereClause,
      include: {
        organization: {
          select: { name: true, slug: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return c.json({
      success: true,
      data: videos
    });
  } catch (error) {
    console.error("Error fetching videos:", error);
    return c.json({
      success: false,
      error: "Failed to fetch videos"
    }, 500);
  }
});

/**
 * GET /api/video/:videoId
 * Get a specific video
 */
app.get("/:videoId", requireAuth, async (c) => {
  try {
    const videoId = c.req.param("videoId");

    const video = await prisma.video.findUnique({
      where: { id: videoId },
      include: {
        organization: {
          select: { name: true, slug: true }
        }
      }
    });

    if (!video) {
      return c.json({
        success: false,
        error: "Video not found"
      }, 404);
    }

    return c.json({
      success: true,
      data: video
    });
  } catch (error) {
    console.error("Error fetching video:", error);
    return c.json({
      success: false,
      error: "Failed to fetch video"
    }, 500);
  }
});

/**
 * POST /api/video
 * Create a new video (Admin/SuperAdmin only)
 */
app.post("/", requireAuth, requireRole("admin"), async (c) => {
  try {
    const body = await c.req.json();
    const { title, youtubeUrl, organizationId, visitorType } = body;

    // Validate required fields
    if (!title || !youtubeUrl || !organizationId || !visitorType) {
      return c.json({
        success: false,
        error: "Missing required fields"
      }, 400);
    }

    // Verify organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      return c.json({
        success: false,
        error: "Organization not found"
      }, 404);
    }

    // Validate YouTube URL
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    if (!youtubeRegex.test(youtubeUrl)) {
      return c.json({
        success: false,
        error: "Invalid YouTube URL"
      }, 400);
    }

    const video = await prisma.video.create({
      data: {
        title,
        youtubeUrl,
        organizationId,
        visitorType: visitorType === 'KARYAWAN' || visitorType === 'karyawan' ? 'KARYAWAN' : 'UMUM'
      },
      include: {
        organization: {
          select: { name: true, slug: true }
        }
      }
    });

    return c.json({
      success: true,
      data: video
    });
  } catch (error) {
    console.error("Error creating video:", error);
    return c.json({
      success: false,
      error: "Failed to create video"
    }, 500);
  }
});

/**
 * PUT /api/video/:videoId
 * Update a video (Admin/SuperAdmin only)
 */
app.put("/:videoId", requireAuth, requireRole("admin"), async (c) => {
  try {
    const videoId = c.req.param("videoId");
    const body = await c.req.json();
    const { title, youtubeUrl, visitorType } = body;

    // Check if video exists
    const existingVideo = await prisma.video.findUnique({
      where: { id: videoId }
    });

    if (!existingVideo) {
      return c.json({
        success: false,
        error: "Video not found"
      }, 404);
    }

    // Validate YouTube URL if provided
    if (youtubeUrl) {
      const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
      if (!youtubeRegex.test(youtubeUrl)) {
        return c.json({
          success: false,
          error: "Invalid YouTube URL"
        }, 400);
      }
    }

    const updatedVideo = await prisma.video.update({
      where: { id: videoId },
      data: {
        ...(title && { title }),
        ...(youtubeUrl && { youtubeUrl }),
        ...(visitorType && { visitorType: visitorType === 'KARYAWAN' || visitorType === 'karyawan' ? 'KARYAWAN' : 'UMUM' })
      },
      include: {
        organization: {
          select: { name: true, slug: true }
        }
      }
    });

    return c.json({
      success: true,
      data: updatedVideo
    });
  } catch (error) {
    console.error("Error updating video:", error);
    return c.json({
      success: false,
      error: "Failed to update video"
    }, 500);
  }
});

/**
 * DELETE /api/video/:videoId
 * Delete a video (Admin/SuperAdmin only)
 */
app.delete("/:videoId", requireAuth, requireRole("admin"), async (c) => {
  try {
    const videoId = c.req.param("videoId");

    // Check if video exists
    const existingVideo = await prisma.video.findUnique({
      where: { id: videoId }
    });

    if (!existingVideo) {
      return c.json({
        success: false,
        error: "Video not found"
      }, 404);
    }

    await prisma.video.delete({
      where: { id: videoId }
    });

    return c.json({
      success: true,
      message: "Video deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting video:", error);
    return c.json({
      success: false,
      error: "Failed to delete video"
    }, 500);
  }
});

export default app;
