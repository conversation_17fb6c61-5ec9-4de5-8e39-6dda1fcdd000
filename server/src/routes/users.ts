import { Hono } from "hono";
import { requireAuth, requireSuperAdmin, requireAdmin, requireUser } from "../middleware/auth";
import { UserService } from "../services/userService";
import { OrganizationService } from "../services/organizationService";
import { auth } from "../lib/auth";

// Define context variables type
type AppVariables = {
  user: any;
  session: any;
};

export const userRoutes = new Hono<{ Variables: AppVariables }>()

// Get all users with their organizations (superadmin only)
.get("/", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const result = await UserService.getAllUsers();
    
    const formattedUsers = result.users.map((user: any) => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      organizations: user.organizations.map((org: any) => ({
        id: org.id,
        name: org.name,
        slug: org.slug,
        role: org.role
      })),
      createdAt: new Date(user.createdAt).toLocaleString('id-ID', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }));
    
    return c.json({ success: true, data: formattedUsers });
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({ success: false, error: "Failed to fetch users" }, 500);
  }
})

// Set superadmin role
.post("/set-superadmin-role", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const { userId } = await c.req.json();
    
    if (!userId) {
      return c.json({ success: false, error: 'User ID is required' }, 400);
    }

    // Update user role to superadmin
    const result = await UserService.updateUserRole(userId, 'superadmin');

    // Remove user from all organizations (superadmin should not be a member of any organization)
    // Get user organizations first, then remove from each
    const userOrgs = await OrganizationService.getUserOrganizations(userId);
    for (const org of userOrgs) {
      await OrganizationService.removeMember(userId, org.id);
    }

    return c.json({ success: true, user: result });
  } catch (error) {
    console.error('Error setting superadmin role:', error);
    return c.json({ success: false, error: 'Failed to set superadmin role' }, 500);
  }
})

// Create superadmin user
.post("/create-superadmin-user", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const { name, email, password } = await c.req.json();
    
    if (!name || !email || !password) {
      return c.json({ success: false, error: 'Name, email, and password are required' }, 400);
    }

    // Create superadmin user
    const newUser = await UserService.createUserWithOrganization({
      name,
      email,
      password,
      role: 'superadmin'
    });
    
    return c.json({ success: true, user: newUser });
  } catch (error: any) {
     console.error('Error creating superadmin user:', error);
     return c.json({ success: false, error: 'Failed to create superadmin user' }, 500);
   }
})

// Update superadmin user
.post("/update-superadmin-user", requireAuth, requireSuperAdmin, async (c) => {
  try {
    const { userId, name, email } = await c.req.json();
    
    if (!userId) {
      return c.json({ success: false, error: 'User ID is required' }, 400);
    }

    const updateData: any = {
      role: 'superadmin'
    };

    if (name) updateData.name = name;
    if (email) updateData.email = email;

    // Update user
    const updatedUser = await UserService.updateUser(userId, updateData);

    // Remove user from all organizations (superadmin should not be a member of any organization)
    const userOrgs = await OrganizationService.getUserOrganizations(userId);
    for (const org of userOrgs) {
      await OrganizationService.removeMember(userId, org.id);
    }
    
    return c.json({ success: true, user: updatedUser });
  } catch (error: any) {
     console.error('Error updating superadmin user:', error);
     if (error.code === 'P2002') {
       return c.json({ success: false, error: 'Email already exists' }, 400);
     } else {
       return c.json({ success: false, error: 'Failed to update superadmin user' }, 500);
     }
   }
})

// Custom endpoint to handle role updates with organization management
.post("/update-user-role", requireAuth, requireAdmin, async (c) => {
  try {
    const { userId, role, organizationId } = await c.req.json();
    
    console.log('🔍 Update user role request:', { userId, role, organizationId });
    
    if (!userId || !role) {
      return c.json({ success: false, error: 'User ID and role are required' }, 400);
    }

    // Update user role using better-auth API
    const updatedUser = await auth.api.setRole({
      headers: c.req.raw.headers,
      body: { userId, role },
    });
    
    console.log('✅ User role updated via better-auth:', { userId, newRole: role });

    // Handle organization membership based on role
    console.log('🔍 Checking organization membership conditions:', { 
      role, 
      organizationId,
      isSuperadmin: role === 'superadmin'
    });

    if (role === 'superadmin') {
      // Superadmin should not be a member of any organization
      console.log('🔄 Removing superadmin from all organizations...');
      const userOrgs = await OrganizationService.getUserOrganizations(userId);
      for (const org of userOrgs) {
        await OrganizationService.removeMember(userId, org.id);
      }
      console.log('✅ Superadmin removed from all organizations');
    } else if (organizationId && ['admin', 'user'].includes(role)) {
      // For admin/user roles, ensure they are a member of the specified organization
      console.log('🔍 Checking if user is member of organization:', organizationId);
      
      // Check if user is already a member by getting their organizations
      const userOrgs = await OrganizationService.getUserOrganizations(userId);
      const existingMember = userOrgs.find(org => org.id === organizationId);
      
      if (!existingMember) {
        console.log(`➕ Adding user to organization with role: ${role}...`);
        await OrganizationService.addMember(userId, organizationId, role);
        console.log('✅ User added to organization');
      } else {
        console.log(`🔄 Updating existing member role from ${existingMember.role} to ${role}...`);
        await OrganizationService.updateMemberRole(userId, organizationId, role);
        console.log('✅ Member role updated to match user role');
      }
    }

    return c.json({ 
      success: true, 
      user: updatedUser,
      message: `User role updated to ${role} successfully`
    });
  } catch (error: any) {
    console.error('❌ Error updating user role:', error);
    return c.json({ success: false, error: 'Failed to update user role' }, 500);
  }
});

// Protected routes
userRoutes.get("/superadmin", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ message: "Superadmin access granted", user: c.get("user") }, { status: 200 });
});

userRoutes.get("/admin", requireAuth, requireAdmin, async (c) => {
	return c.json({ message: "Admin access granted", user: c.get("user") }, { status: 200 });
});

userRoutes.get("/user", requireAuth, async (c) => {
	return c.json({ message: "User access granted", user: c.get("user") }, { status: 200 });
});

// Legacy route for frontend compatibility
userRoutes.get("/user-organizations/:userId", requireAuth, async (c) => {
	try {
		const userId = c.req.param('userId');
		const organizations = await OrganizationService.getUserOrganizations(userId);
		return c.json({ success: true, organizations });
	} catch (error) {
		console.error('Error fetching user organizations:', error);
		return c.json({ success: false, error: error instanceof Error ? error.message : "Internal server error" }, 500);
	}
});

export default userRoutes;