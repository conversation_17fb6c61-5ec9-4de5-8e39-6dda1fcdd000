import { <PERSON>o } from "hono";
import { PrismaClient } from "@prisma/client";
import { requireAuth, requireRole } from "../middleware/auth";

const prisma = new PrismaClient();
const app = new Hono();

/**
 * GET /api/quiz/organizations/:orgId/quizzes
 * Get all quizzes for an organization
 */
app.get("/organizations/:orgId/quizzes", requireAuth, async (c) => {
  try {
    const orgId = c.req.param("orgId");
    const visitorType = c.req.query("visitorType"); // Optional filter

    const whereClause: any = {
      organizationId: orgId,
      isActive: true
    };

    if (visitorType && (visitorType === 'UMUM' || visitorType === 'KARYAWAN')) {
      whereClause.visitorType = visitorType;
    }

    const quizzes = await prisma.quiz.findMany({
      where: whereClause,
      include: {
        questions: {
          orderBy: { order: 'asc' }
        },
        organization: {
          select: { name: true, slug: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return c.json({
      success: true,
      data: quizzes
    });
  } catch (error) {
    console.error("Error fetching quizzes:", error);
    return c.json({
      success: false,
      error: "Failed to fetch quizzes"
    }, 500);
  }
});

/**
 * GET /api/quiz/:quizId
 * Get a specific quiz with questions
 */
app.get("/:quizId", requireAuth, async (c) => {
  try {
    const quizId = c.req.param("quizId");

    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        },
        organization: {
          select: { name: true, slug: true }
        }
      }
    });

    if (!quiz) {
      return c.json({
        success: false,
        error: "Quiz not found"
      }, 404);
    }

    return c.json({
      success: true,
      data: quiz
    });
  } catch (error) {
    console.error("Error fetching quiz:", error);
    return c.json({
      success: false,
      error: "Failed to fetch quiz"
    }, 500);
  }
});

/**
 * POST /api/quiz
 * Create a new quiz (Admin/SuperAdmin only)
 */
app.post("/", requireAuth, requireRole("admin"), async (c) => {
  try {
    const body = await c.req.json();
    const { title, description, organizationId, visitorType, questions } = body;

    // Validate required fields
    if (!title || !organizationId || !visitorType) {
      return c.json({
        success: false,
        error: "Missing required fields"
      }, 400);
    }

    // Verify organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });

    if (!organization) {
      return c.json({
        success: false,
        error: "Organization not found"
      }, 404);
    }

    // Create quiz with questions
    const quiz = await prisma.quiz.create({
      data: {
        title,
        description,
        organizationId,
        visitorType: visitorType === 'KARYAWAN' || visitorType === 'karyawan' ? 'KARYAWAN' : 'UMUM',
        questions: {
          create: questions?.map((q: any, index: number) => ({
            question: q.question,
            options: q.options,
            correctAnswer: q.correctAnswer,
            order: index
          })) || []
        }
      },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        }
      }
    });

    return c.json({
      success: true,
      data: quiz
    });
  } catch (error) {
    console.error("Error creating quiz:", error);
    return c.json({
      success: false,
      error: "Failed to create quiz"
    }, 500);
  }
});

/**
 * PUT /api/quiz/:quizId
 * Update a quiz (Admin/SuperAdmin only)
 */
app.put("/:quizId", requireAuth, requireRole("admin"), async (c) => {
  try {
    const quizId = c.req.param("quizId");
    const body = await c.req.json();
    const { title, description, visitorType, questions } = body;

    // Check if quiz exists
    const existingQuiz = await prisma.quiz.findUnique({
      where: { id: quizId }
    });

    if (!existingQuiz) {
      return c.json({
        success: false,
        error: "Quiz not found"
      }, 404);
    }

    // Update quiz and replace questions
    const updatedQuiz = await prisma.$transaction(async (tx) => {
      // Delete existing questions
      await tx.question.deleteMany({
        where: { quizId }
      });

      // Update quiz and create new questions
      return await tx.quiz.update({
        where: { id: quizId },
        data: {
          title,
          description,
          visitorType: visitorType === 'KARYAWAN' || visitorType === 'karyawan' ? 'KARYAWAN' : 'UMUM',
          questions: {
            create: questions?.map((q: any, index: number) => ({
              question: q.question,
              options: q.options,
              correctAnswer: q.correctAnswer,
              order: index
            })) || []
          }
        },
        include: {
          questions: {
            orderBy: { order: 'asc' }
          }
        }
      });
    });

    return c.json({
      success: true,
      data: updatedQuiz
    });
  } catch (error) {
    console.error("Error updating quiz:", error);
    return c.json({
      success: false,
      error: "Failed to update quiz"
    }, 500);
  }
});

/**
 * DELETE /api/quiz/:quizId
 * Delete a quiz (Admin/SuperAdmin only)
 */
app.delete("/:quizId", requireAuth, requireRole("admin"), async (c) => {
  try {
    const quizId = c.req.param("quizId");

    // Check if quiz exists
    const existingQuiz = await prisma.quiz.findUnique({
      where: { id: quizId }
    });

    if (!existingQuiz) {
      return c.json({
        success: false,
        error: "Quiz not found"
      }, 404);
    }

    // Delete quiz (questions will be deleted due to cascade)
    await prisma.quiz.delete({
      where: { id: quizId }
    });

    return c.json({
      success: true,
      message: "Quiz deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting quiz:", error);
    return c.json({
      success: false,
      error: "Failed to delete quiz"
    }, 500);
  }
});

/**
 * POST /api/quiz/:quizId/submit
 * Submit quiz answers and calculate score
 */
app.post("/:quizId/submit", requireAuth, async (c) => {
  try {
    const quizId = c.req.param("quizId");
    const body = await c.req.json();
    const { answers } = body; // Array of answer indices
    const user: any = (c as any).get("user");

    // Get quiz with questions
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: {
          orderBy: { order: 'asc' }
        }
      }
    });

    if (!quiz) {
      return c.json({
        success: false,
        error: "Quiz not found"
      }, 404);
    }

    // Calculate score
    let correctAnswers = 0;
    const totalQuestions = quiz.questions.length;

    quiz.questions.forEach((question, index) => {
      if (answers[index] === question.correctAnswer) {
        correctAnswers++;
      }
    });

    const score = Math.round((correctAnswers / totalQuestions) * 100);
    const isPassed = score >= 80; // 80% passing score

    // Save quiz result
    const quizResult = await prisma.quizResult.create({
      data: {
        userId: user.id,
        quizId,
        score,
        answers: JSON.stringify(answers),
        isPassed
      }
    });

    return c.json({
      success: true,
      data: {
        score,
        isPassed,
        correctAnswers,
        totalQuestions,
        resultId: quizResult.id
      }
    });
  } catch (error) {
    console.error("Error submitting quiz:", error);
    return c.json({
      success: false,
      error: "Failed to submit quiz"
    }, 500);
  }
});

/**
 * GET /api/quiz/user/:userId/results
 * Get quiz results for a user
 */
app.get("/user/:userId/results", requireAuth, async (c) => {
  try {
    const userId = c.req.param("userId");
    const user: any = (c as any).get("user");

    // Users can only access their own results, admins can access any
    if (user.id !== userId && user.role !== 'admin' && user.role !== 'superadmin') {
      return c.json({
        success: false,
        error: "Unauthorized"
      }, 403);
    }

    const results = await prisma.quizResult.findMany({
      where: { userId },
      include: {
        quiz: {
          include: {
            organization: {
              select: { name: true, slug: true }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return c.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error("Error fetching quiz results:", error);
    return c.json({
      success: false,
      error: "Failed to fetch quiz results"
    }, 500);
  }
});

export default app;
