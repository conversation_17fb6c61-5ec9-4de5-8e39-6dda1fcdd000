import { <PERSON>o } from "hono";
import { requireAuth, requireSuperAdmin, requireAdmin, requireUser } from "../middleware/auth";

// Define context variables type
type AppVariables = {
  user: any;
  session: any;
};

const protectedApp = new Hono<{ Variables: AppVariables }>()

// Protected routes with authentication
.get("/profile", requireAuth, async (c) => {
	const user = c.get("user");
	return c.json({ user });
})

// Superadmin protected routes (highest privilege)
.get("/superadmin/users", requireAuth, requireSuperAdmin, async (c) => {
	// This would fetch users from database in real implementation
	return c.json({ message: "Superadmin users endpoint", users: [] }, { status: 200 });
})

// Admin protected routes (admin and superadmin can access)
.get("/admin/dashboard", requireAuth, requireAdmin, async (c) => {
	// This would fetch admin dashboard data in real implementation
	return c.json({ message: "Admin dashboard endpoint", data: {} }, { status: 200 });
})

// User protected routes (all authenticated users can access)
.get("/user/profile", requireAuth, requireUser, async (c) => {
	const user = c.get("user");
	return c.json({ user });
})

.get("/admin/roles", requireAuth, requireAdmin, async (c) => {
	return c.json([], { status: 200 });
})

.post("/admin/roles/create", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ success: true, message: "UI only - no implementation" });
})

.put("/admin/roles/update", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ success: true, message: "UI only - no implementation" });
})

.delete("/admin/roles/delete", requireAuth, requireSuperAdmin, async (c) => {
	return c.json({ success: true, message: "UI only - no implementation" });
});

export default protectedApp;