import { Hono } from "hono";
import type { ApiResponse, DashboardStats, ChartDataPoint, VisitorData } from "../types/shared";
import { requireAuth, requireUser } from "../middleware/auth";
import { DashboardService } from "../services/dashboardService";

const dashboardApp = new Hono();

dashboardApp.get("/stats", requireAuth, requireUser, async (c) => {
	const stats: DashboardStats = {
		totalPengunjungTerdaftar: 17,
		totalPengunjungDisetujui: 10,
		totalPengunjungDitolak: 2,
		totalPengunjungKadaluwarsa: 5,
	};

	return c.json(stats, { status: 200 });
})

.get("/chart", requireAuth, requireUser, async (c) => {
	const chartData: ChartDataPoint[] = [
		{ day: "Senin", value: 40 },
		{ day: "Selasa", value: 35 },
		{ day: "Rabu", value: 45 },
		{ day: "Kamis", value: 95 },
		{ day: "Jumat", value: 80 },
		{ day: "Sabtu", value: 65 },
	];

	return c.json(chartData, { status: 200 });
})

.get("/chart/weekly", requireAuth, requireUser, async (c) => {
	const weeklyData: ChartDataPoint[] = [
		{ day: "Minggu 1", value: 280 },
		{ day: "Minggu 2", value: 320 },
		{ day: "Minggu 3", value: 290 },
		{ day: "Minggu 4", value: 410 },
	];

	return c.json(weeklyData, { status: 200 });
})

.get("/visitors", requireAuth, requireUser, async (c) => {
	const visitors: VisitorData[] = [
		{
			id: "1",
			name: "Rina Pratama",
			company: "PT. Teknologi Maju",
			type: "PAM",
			status: "approved",
			date: "2024-01-15",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "2",
			name: "Budi Santoso",
			company: "CV. Berkah Jaya",
			type: "PAM",
			status: "pending",
			date: "2024-01-16",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "3",
			name: "Sari Wijaya",
			company: "PT. Industri Besar",
			type: "PAM",
			status: "approved",
			date: "2024-01-17",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "4",
			name: "Ahmad Fauzi",
			company: "PT. Maju Bersama",
			type: "PAM",
			status: "approved",
			date: "2024-01-18",
			location: { lat: -6.2088, lng: 106.8456 }
		},
		{
			id: "5",
			name: "Dewi Sartika",
			company: "CV. Sukses Mandiri",
			type: "SMA",
			status: "pending",
			date: "2024-01-19"
		},
		{
			id: "6",
			name: "Rudi Hermawan",
			company: "PT. Global Tech",
			type: "SMA",
			status: "approved",
			date: "2024-01-20",
			location: { lat: -6.1751, lng: 106.8650 }
		},
		{
			id: "7",
			name: "Maya Sari",
			company: "PT. Digital Solutions",
			type: "IBM",
			status: "rejected",
			date: "2024-01-21"
		},
		{
			id: "8",
			name: "Andi Wijaya",
			company: "CV. Inovasi Teknologi",
			type: "IBM",
			status: "approved",
			date: "2024-01-22",
			location: { lat: -6.2088, lng: 106.8456 }
		}
	];

	return c.json(visitors, { status: 200 });
});

// Legacy dashboard routes (for backward compatibility)
dashboardApp.get("/chart", requireAuth, requireUser, async (c) => {
	try {
		const chartData = await DashboardService.getChartData('daily');
		if (chartData.datasets && chartData.datasets[0] && chartData.labels) {
			return c.json(chartData.datasets[0].data.map((value, index) => ({
				day: chartData.labels[index],
				value
			})), { status: 200 });
		}
		return c.json({ error: "Invalid chart data structure" }, 500);
	} catch (error) {
		console.error('Error fetching chart data:', error);
		return c.json({ error: "Failed to fetch chart data" }, 500);
	}
});

dashboardApp.get("/chart/weekly", requireAuth, requireUser, async (c) => {
	try {
		const chartData = await DashboardService.getChartData('weekly');
		if (chartData.datasets && chartData.datasets[0] && chartData.labels) {
			return c.json(chartData.datasets[0].data.map((value, index) => ({
				day: chartData.labels[index],
				value
			})), { status: 200 });
		}
		return c.json({ error: "Invalid chart data structure" }, 500);
	} catch (error) {
		console.error('Error fetching weekly chart data:', error);
		return c.json({ error: "Failed to fetch weekly chart data" }, 500);
	}
});

export default dashboardApp;