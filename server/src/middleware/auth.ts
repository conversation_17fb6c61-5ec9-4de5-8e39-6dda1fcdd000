import { auth } from "../lib/auth";
import type { Context, Next } from "hono";

/**
 * Middleware to check if user is authenticated
 */
export const requireAuth = async (c: Context, next: Next) => {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    console.log("Auth middleware - session:", session ? "found" : "not found");
    console.log("Auth middleware - headers:", Object.fromEntries(c.req.raw.headers.entries()));

    if (!session) {
      console.log("Auth middleware - no session, returning 401");
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Add user and session to context
    c.set("user", session.user);
    c.set("session", session.session);

    console.log("Auth middleware - user authenticated:", session.user.email);
    await next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return c.json({ error: "Authentication failed" }, 401);
  }
};

/**
 * Middleware to check if user has required role
 * Role hierarchy: superadmin (highest) > admin > user (lowest)
 */
export const requireRole = (requiredRole: "user" | "admin" | "superadmin") => {
  return async (c: Context, next: Next) => {
    // First check authentication
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (!session) {
      console.log("Role middleware - no session, returning 401");
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Set user and session in context if not already set
    if (!c.get("user")) {
      c.set("user", session.user);
      c.set("session", session.session);
    }

    const user = session.user;
    const userRole = user.role as string;

    console.log("Role middleware - user role:", userRole, "required:", requiredRole);

    // Role hierarchy: superadmin (3) > admin (2) > user (1)
    const roleHierarchy = {
      user: 1,
      admin: 2,
      superadmin: 3
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    // User must have equal or higher level than required
    if (userLevel < requiredLevel) {
      console.log("Role middleware - insufficient permissions:", userLevel, "vs", requiredLevel);
      return c.json({ error: "Insufficient permissions" }, 403);
    }

    console.log("Role middleware - access granted");
    await next();
  };
};

/**
 * Middleware to check if user has one of the allowed roles
 */
export const requireAnyRole = (allowedRoles: ("user" | "admin" | "superadmin")[]) => {
  return async (c: Context, next: Next) => {
    const user = c.get("user");

    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const userRole = user.role as string;

    if (!allowedRoles.includes(userRole as any)) {
      return c.json({ error: "Insufficient permissions" }, 403);
    }

    await next();
  };
};

/**
 * Middleware to check if user is superadmin (highest privilege)
 */
export const requireSuperAdmin = requireRole("superadmin");

/**
 * Middleware to check if user is admin or higher
 */
export const requireAdmin = requireRole("admin");

/**
 * Middleware to check if user is authenticated (any role)
 */
export const requireUser = requireRole("user");