/**
 * CORS configuration for the application
 */
export const corsConfig = {
  origin: (origin) => {
    const allowedOrigins = [
      "http://localhost:5173",
      "http://localhost:3000",
      "https://safety-induction.netlify.app",
      ...(process.env.CORS_ORIGIN?.split(',') || [])
    ].filter(Boolean);

    // Allow Netlify preview URLs
    if (origin && origin.match(/https:\/\/.*\.netlify\.app$/)) {
      return origin;
    }

    return allowedOrigins.includes(origin) ? origin : null;
  },
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['set-cookie'],
};