import { Hono } from "hono";
// CORS removed for single origin deployment
import { serveStatic } from "hono/bun";
import { auth } from "./lib/auth";

// Import routes
import adminApp from "./routes/admin";
import authApp from "./routes/auth";
import dashboardApp from "./routes/dashboard";
import organizationApp from "./routes/organization";
import organizationsApp from "./routes/organizations";
import protectedApp from "./routes/protected";
import quizApp from "./routes/quiz";
import userApp from "./routes/user";
import usersApp from "./routes/users";
import videoApp from "./routes/video";

// Define context variables type
type AppVariables = {
  user: any;
  session: any;
};

const app = new Hono<{ Variables: AppVariables }>();

// CORS completely removed for single origin deployment

// Mount route modules first (custom routes have priority)
app.route("/api/admin", adminApp);
app.route("/api/auth", authApp);
app.route("/api/dashboard", dashboardApp);
app.route("/api/organization", organizationApp);
app.route("/api/organizations", organizationsApp);
app.route("/api/protected", protectedApp);
app.route("/api/quiz", quizApp);
app.route("/api/user", userApp);
app.route("/api/users", usersApp);
app.route("/api/video", videoApp);



// Basic routes
app.get("/api/health", (c) => {
	return c.json({ status: "OK", timestamp: new Date().toISOString() });
});

// Serve static files for single origin deployment
app.use("*", serveStatic({ root: "./static" }));

// SPA fallback - serve index.html for all non-API routes
app.get("*", async (c, next) => {
	return serveStatic({ root: "./static", path: "index.html" })(c, next);
});





// Server configuration
const port = process.env.PORT || 3000;

// Export app for testing
export { app };

// Default export for Bun runtime
const serverConfig = {
	port,
	fetch: app.fetch,
};

export default serverConfig;

// Start server
if (typeof Bun !== "undefined") {
	// Bun runtime
	console.log(`🚀 Server running on http://localhost:${port}`);
} else {
	// Node.js runtime
	const { serve } = require("@hono/node-server");
	serve({
		fetch: app.fetch,
		port: Number(port),
	});
	console.log(`🚀 Server running on http://localhost:${port}`);
}