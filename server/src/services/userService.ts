import { PrismaClient } from "@prisma/client";
import { generateId } from "better-auth";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

export class UserService {
  // Create user with organization assignment (superadmin only)
  static async createUserWithOrganization(userData: {
    name: string;
    email: string;
    password: string;
    role: string;
    organizationId?: string;
  }) {
    const { name, email, password, role, organizationId } = userData;
    
    // Validate required fields
    if (!name || !email || !password || !role) {
      throw new Error("Missing required fields: name, email, password, role");
    }
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      throw new Error("User with this email already exists");
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create user
    const user = await prisma.user.create({
      data: {
        id: generateId(),
        name,
        email,
        emailVerified: true, // Auto-verify for admin created users
        role,
        createdAt: new Date()
      }
    });
    
    // Create account record for password
    await prisma.account.create({
      data: {
        id: generateId(),
        userId: user.id,
        accountId: user.id,
        providerId: "credential",
        password: hashedPassword,
        createdAt: new Date()
      }
    });
    
    // If organization is specified, add user as member
    if (organizationId) {
      // Verify organization exists
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId }
      });
      
      if (!organization) {
        // Clean up created user if organization doesn't exist
        await prisma.account.deleteMany({ where: { userId: user.id } });
        await prisma.user.delete({ where: { id: user.id } });
        throw new Error("Organization not found");
      }
      
      // Add user to organization
      await prisma.member.create({
        data: {
          id: generateId(),
          userId: user.id,
          organizationId,
          role: role === 'superadmin' ? 'admin' : 'member', // Superadmins become admins in organizations
          createdAt: new Date()
        }
      });
    }
    
    // Return user without sensitive data
    const { ...userWithoutPassword } = user;
    return {
      ...userWithoutPassword,
      organizationId: organizationId || null
    };
  }

  // Update user role (superadmin only)
  static async updateUserRole(userId: string, newRole: string) {
    // Validate input
    if (!userId || !newRole) {
      throw new Error("User ID and role are required");
    }
    
    // Validate role
    const validRoles = ['user', 'admin', 'superadmin'];
    if (!validRoles.includes(newRole)) {
      throw new Error("Invalid role. Must be one of: user, admin, superadmin");
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Update user role
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { role: newRole }
    });
    
    return {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      message: `User role updated to ${newRole} successfully`
    };
  }

  // Get all users with organization info (superadmin only)
  static async getAllUsers(page: number = 1, limit: number = 10, search?: string) {
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const whereClause = search ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' as const } },
        { email: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {};
    
    // Get users with their organization memberships
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          members: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                  slug: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.user.count({ where: whereClause })
    ]);
    
    // Transform data to include organization info
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      organizations: user.members.map(member => ({
        id: member.organization.id,
        name: member.organization.name,
        slug: member.organization.slug,
        role: member.role,
        joinedAt: member.createdAt
      }))
    }));
    
    return {
      users: transformedUsers,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    };
  }

  // Get user by ID with organization info
  static async getUserById(userId: string) {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        members: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true
              }
            }
          }
        }
      }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Transform data
    const transformedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt,
      organizations: user.members.map(member => ({
        id: member.organization.id,
        name: member.organization.name,
        slug: member.organization.slug,
        role: member.role,
        joinedAt: member.createdAt
      }))
    };
    
    return transformedUser;
  }

  // Update user data (superadmin only)
  static async updateUser(userId: string, updateData: {
    name?: string;
    email?: string;
    role?: string;
  }) {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // If email is being updated, check for conflicts
    if (updateData.email && updateData.email !== user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: updateData.email }
      });
      
      if (existingUser) {
        throw new Error("Email already exists");
      }
    }
    
    // Validate role if provided
    if (updateData.role) {
      const validRoles = ['user', 'admin', 'superadmin'];
      if (!validRoles.includes(updateData.role)) {
        throw new Error("Invalid role. Must be one of: user, admin, superadmin");
      }
    }
    
    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(updateData.name && { name: updateData.name }),
        ...(updateData.email && { email: updateData.email }),
        ...(updateData.role && { role: updateData.role })
      }
    });
    
    return {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      emailVerified: updatedUser.emailVerified,
      createdAt: updatedUser.createdAt
    };
  }

  // Delete user (superadmin only)
  static async deleteUser(userId: string) {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            members: true,
            accounts: true,
            sessions: true
          }
        }
      }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Delete related records in order (cascade delete)
    await prisma.$transaction(async (tx) => {
      // Delete sessions
      await tx.session.deleteMany({
        where: { userId }
      });
      
      // Delete accounts
      await tx.account.deleteMany({
        where: { userId }
      });
      
      // Delete organization memberships
      await tx.member.deleteMany({
        where: { userId }
      });
      
      // Delete user
      await tx.user.delete({
        where: { id: userId }
      });
    });
    
    return {
      message: `User '${user.name}' deleted successfully along with ${user._count.members} organization memberships, ${user._count.accounts} accounts, and ${user._count.sessions} sessions`
    };
  }

  // Update user password (superadmin only)
  static async updateUserPassword(userId: string, newPassword: string) {
    if (!userId || !newPassword) {
      throw new Error("User ID and new password are required");
    }
    
    // Validate password strength
    if (newPassword.length < 6) {
      throw new Error("Password must be at least 6 characters long");
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update password in account record
    const updatedAccount = await prisma.account.updateMany({
      where: {
        userId,
        providerId: "credential"
      },
      data: {
        password: hashedPassword
      }
    });
    
    if (updatedAccount.count === 0) {
      throw new Error("No credential account found for this user");
    }
    
    return {
      message: "Password updated successfully"
    };
  }
}