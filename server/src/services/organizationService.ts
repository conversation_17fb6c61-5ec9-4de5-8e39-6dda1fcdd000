import { PrismaClient } from "@prisma/client";
import { generateId } from "better-auth";

const prisma = new PrismaClient();

export class OrganizationService {
  // Add member to organization
  static async addMember(userId: string, organizationId: string, role: string) {
    // Validate input
    if (!userId || !organizationId || !role) {
      throw new Error("Missing required fields");
    }
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId }
    });
    
    if (!organization) {
      throw new Error("Organization not found");
    }
    
    // Check if user is already a member
    const existingMember = await prisma.member.findFirst({
      where: {
        userId,
        organizationId
      }
    });
    
    if (existingMember) {
      throw new Error("User is already a member of this organization");
    }
    
    // Create member record
    const member = await prisma.member.create({
      data: {
        id: generateId(),
        userId,
        organizationId,
        role,
        createdAt: new Date()
      }
    });
    
    return member;
  }

  // Update member role in organization
  static async updateMemberRole(userId: string, organizationId: string, role: string) {
    // Validate input
    if (!userId || !organizationId || !role) {
      throw new Error("Missing required fields");
    }
    
    // Update member role
    const member = await prisma.member.updateMany({
      where: {
        userId,
        organizationId
      },
      data: {
        role
      }
    });
    
    if (member.count === 0) {
      throw new Error("Member not found");
    }
    
    return { message: "Member role updated successfully" };
  }

  // Remove member from organization
  static async removeMember(userId: string, organizationId: string) {
    // Validate input
    if (!userId || !organizationId) {
      throw new Error("Missing required fields");
    }
    
    // Remove member from organization
    const result = await prisma.member.deleteMany({
      where: {
        userId,
        organizationId
      }
    });
    
    if (result.count === 0) {
      throw new Error("Member not found");
    }
    
    return { message: "Member removed from organization successfully" };
  }

  // Get user organizations
  static async getUserOrganizations(userId: string) {
    if (!userId) {
      throw new Error("User ID is required");
    }
    
    // Get user's organizations with member details
    const userOrganizations = await prisma.member.findMany({
      where: {
        userId
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo: true,
            createdAt: true
          }
        }
      }
    });
    
    // Transform the data to match expected format
    const organizations = userOrganizations.map(member => ({
      id: member.organization.id,
      name: member.organization.name,
      slug: member.organization.slug,
      logo: member.organization.logo,
      role: member.role,
      joinedAt: member.createdAt,
      createdAt: member.organization.createdAt
    }));
    
    return organizations;
  }

  // Create organization (superadmin only)
  static async createOrganization(name: string, slug?: string, logo?: string, metadata?: any) {
    // Validate input
    if (!name) {
      throw new Error("Organization name is required");
    }
    
    // Generate slug if not provided
    const organizationSlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    
    // Check if slug already exists
    const existingOrg = await prisma.organization.findUnique({
      where: { slug: organizationSlug }
    });
    
    if (existingOrg) {
      throw new Error("Organization slug already exists");
    }
    
    // Create organization without adding superadmin as member
    const organization = await prisma.organization.create({
      data: {
        id: generateId(),
        name,
        slug: organizationSlug,
        logo,
        metadata: metadata ? JSON.stringify(metadata) : null,
        createdAt: new Date()
      }
    });
    
    return organization;
  }

  // Get all organizations (superadmin only)
  static async getAllOrganizations() {
    const organizations = await prisma.organization.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        logo: true,
        createdAt: true,
        _count: {
          select: {
            members: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return organizations;
  }

  // Update organization (superadmin only)
  static async updateOrganization(organizationId: string, name: string, slug?: string, logo?: string, metadata?: any) {
    // Validate input
    if (!name) {
      throw new Error("Organization name is required");
    }
    
    // Generate slug if not provided
    const organizationSlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    
    // Check if slug already exists (excluding current organization)
    const existingOrg = await prisma.organization.findFirst({
      where: { 
        slug: organizationSlug,
        id: { not: organizationId }
      }
    });
    
    if (existingOrg) {
      throw new Error("Organization slug already exists");
    }
    
    // Update organization
    const organization = await prisma.organization.update({
      where: { id: organizationId },
      data: {
        name,
        slug: organizationSlug,
        logo,
        metadata: metadata ? JSON.stringify(metadata) : null
      }
    });
    
    return organization;
  }

  // Delete organization (superadmin only)
  static async deleteOrganization(organizationId: string) {
    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        _count: {
          select: {
            members: true
          }
        }
      }
    });
    
    if (!organization) {
      throw new Error("Organization not found");
    }
    
    // Delete all members first (cascade delete)
    await prisma.member.deleteMany({
      where: { organizationId }
    });
    
    // Delete the organization
    await prisma.organization.delete({
      where: { id: organizationId }
    });
    
    return { 
      message: `Organization '${organization.name}' deleted successfully along with ${organization._count.members} members` 
    };
  }

  // Get organization by slug
  static async getOrganizationBySlug(slug: string) {
    const organizationSlug = slug?.trim().toLowerCase();
    
    if (!organizationSlug) {
      throw new Error("Organization slug is required");
    }

    // Find organization by slug using findUnique (since slug has unique constraint)
    const organization = await prisma.organization.findUnique({
      where: {
        slug: organizationSlug
      },
      select: {
        id: true,
        name: true,
        slug: true,
        logo: true,
        createdAt: true
      }
    });

    if (!organization) {
      throw new Error("Organization not found");
    }

    return organization;
  }
}