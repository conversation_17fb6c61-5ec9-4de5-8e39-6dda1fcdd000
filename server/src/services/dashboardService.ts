import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export class DashboardService {
  // Get dashboard statistics
  static async getStats() {
    try {
      // In a real implementation, these would be actual database queries
      // For now, returning dummy data that matches the original implementation
      
      const stats = {
        totalVisitors: 1250,
        todayVisitors: 45,
        activeUsers: 320,
        completedInductions: 890
      };
      
      return stats;
    } catch (error) {
      throw new Error(`Failed to fetch dashboard stats: ${error}`);
    }
  }

  // Get chart data for dashboard
  static async getChartData(period: 'daily' | 'weekly' = 'daily') {
    try {
      // In a real implementation, this would query actual visitor data
      // For now, returning dummy data that matches the original implementation
      
      if (period === 'daily') {
        const dailyData = {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          datasets: [{
            label: 'Visitors',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
          }]
        };
        return dailyData;
      } else {
        const weeklyData = {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          datasets: [{
            label: 'Visitors',
            data: [450, 520, 380, 490],
            borderColor: 'rgb(153, 102, 255)',
            backgroundColor: 'rgba(153, 102, 255, 0.2)',
            tension: 0.1
          }]
        };
        return weeklyData;
      }
    } catch (error) {
      throw new Error(`Failed to fetch chart data: ${error}`);
    }
  }

  // Get visitor data for dashboard
  static async getVisitorData() {
    try {
      // In a real implementation, this would query actual visitor records
      // For now, returning dummy data that matches the original implementation
      
      const visitors = [
        {
          id: '1',
          name: 'John Doe',
          company: 'ABC Corp',
          visitDate: new Date('2024-01-15'),
          status: 'completed',
          inductionScore: 95
        },
        {
          id: '2',
          name: 'Jane Smith',
          company: 'XYZ Ltd',
          visitDate: new Date('2024-01-14'),
          status: 'in-progress',
          inductionScore: null
        },
        {
          id: '3',
          name: 'Bob Johnson',
          company: 'Tech Solutions',
          visitDate: new Date('2024-01-13'),
          status: 'completed',
          inductionScore: 88
        },
        {
          id: '4',
          name: 'Alice Brown',
          company: 'Innovation Inc',
          visitDate: new Date('2024-01-12'),
          status: 'completed',
          inductionScore: 92
        },
        {
          id: '5',
          name: 'Charlie Wilson',
          company: 'Future Corp',
          visitDate: new Date('2024-01-11'),
          status: 'pending',
          inductionScore: null
        }
      ];
      
      return visitors;
    } catch (error) {
      throw new Error(`Failed to fetch visitor data: ${error}`);
    }
  }

  // Get heatmap data for dashboard
  static async getHeatmapData() {
    try {
      // In a real implementation, this would query actual activity data
      // For now, returning dummy data that matches the original implementation
      
      const heatmapData = {
        title: 'Visitor Activity Heatmap',
        data: [
          { hour: 8, day: 'Monday', value: 12 },
          { hour: 9, day: 'Monday', value: 25 },
          { hour: 10, day: 'Monday', value: 18 },
          { hour: 11, day: 'Monday', value: 30 },
          { hour: 12, day: 'Monday', value: 15 },
          { hour: 13, day: 'Monday', value: 22 },
          { hour: 14, day: 'Monday', value: 28 },
          { hour: 15, day: 'Monday', value: 20 },
          { hour: 16, day: 'Monday', value: 16 },
          { hour: 17, day: 'Monday', value: 10 },
          
          { hour: 8, day: 'Tuesday', value: 15 },
          { hour: 9, day: 'Tuesday', value: 28 },
          { hour: 10, day: 'Tuesday', value: 22 },
          { hour: 11, day: 'Tuesday', value: 35 },
          { hour: 12, day: 'Tuesday', value: 18 },
          { hour: 13, day: 'Tuesday', value: 25 },
          { hour: 14, day: 'Tuesday', value: 32 },
          { hour: 15, day: 'Tuesday', value: 24 },
          { hour: 16, day: 'Tuesday', value: 19 },
          { hour: 17, day: 'Tuesday', value: 12 },
          
          { hour: 8, day: 'Wednesday', value: 10 },
          { hour: 9, day: 'Wednesday', value: 20 },
          { hour: 10, day: 'Wednesday', value: 16 },
          { hour: 11, day: 'Wednesday', value: 25 },
          { hour: 12, day: 'Wednesday', value: 12 },
          { hour: 13, day: 'Wednesday', value: 18 },
          { hour: 14, day: 'Wednesday', value: 24 },
          { hour: 15, day: 'Wednesday', value: 17 },
          { hour: 16, day: 'Wednesday', value: 14 },
          { hour: 17, day: 'Wednesday', value: 8 },
          
          { hour: 8, day: 'Thursday', value: 18 },
          { hour: 9, day: 'Thursday', value: 32 },
          { hour: 10, day: 'Thursday', value: 26 },
          { hour: 11, day: 'Thursday', value: 40 },
          { hour: 12, day: 'Thursday', value: 22 },
          { hour: 13, day: 'Thursday', value: 30 },
          { hour: 14, day: 'Thursday', value: 38 },
          { hour: 15, day: 'Thursday', value: 28 },
          { hour: 16, day: 'Thursday', value: 24 },
          { hour: 17, day: 'Thursday', value: 15 },
          
          { hour: 8, day: 'Friday', value: 20 },
          { hour: 9, day: 'Friday', value: 35 },
          { hour: 10, day: 'Friday', value: 28 },
          { hour: 11, day: 'Friday', value: 42 },
          { hour: 12, day: 'Friday', value: 25 },
          { hour: 13, day: 'Friday', value: 32 },
          { hour: 14, day: 'Friday', value: 40 },
          { hour: 15, day: 'Friday', value: 30 },
          { hour: 16, day: 'Friday', value: 26 },
          { hour: 17, day: 'Friday', value: 18 }
        ],
        maxValue: 42,
        minValue: 8
      };
      
      return heatmapData;
    } catch (error) {
      throw new Error(`Failed to fetch heatmap data: ${error}`);
    }
  }

  // Get recent visitors for dashboard
  static async getRecentVisitors(limit: number = 10) {
    try {
      // In a real implementation, this would query actual recent visitor records
      // For now, returning dummy data that matches the original implementation
      
      const recentVisitors = [
        {
          id: '1',
          name: 'John Doe',
          company: 'ABC Corp',
          visitTime: new Date('2024-01-15T10:30:00'),
          status: 'completed',
          avatar: null
        },
        {
          id: '2',
          name: 'Jane Smith',
          company: 'XYZ Ltd',
          visitTime: new Date('2024-01-15T09:45:00'),
          status: 'in-progress',
          avatar: null
        },
        {
          id: '3',
          name: 'Bob Johnson',
          company: 'Tech Solutions',
          visitTime: new Date('2024-01-15T08:20:00'),
          status: 'completed',
          avatar: null
        },
        {
          id: '4',
          name: 'Alice Brown',
          company: 'Innovation Inc',
          visitTime: new Date('2024-01-14T16:15:00'),
          status: 'completed',
          avatar: null
        },
        {
          id: '5',
          name: 'Charlie Wilson',
          company: 'Future Corp',
          visitTime: new Date('2024-01-14T14:30:00'),
          status: 'pending',
          avatar: null
        }
      ];
      
      return recentVisitors.slice(0, limit);
    } catch (error) {
      throw new Error(`Failed to fetch recent visitors: ${error}`);
    }
  }

  // Get dashboard summary data
  static async getDashboardSummary() {
    try {
      const [stats, chartData, visitors, heatmap, recentVisitors] = await Promise.all([
        this.getStats(),
        this.getChartData('daily'),
        this.getVisitorData(),
        this.getHeatmapData(),
        this.getRecentVisitors(5)
      ]);
      
      return {
        stats,
        chartData,
        visitors,
        heatmap,
        recentVisitors
      };
    } catch (error) {
      throw new Error(`Failed to fetch dashboard summary: ${error}`);
    }
  }
}