FROM node:20-alpine

# Install necessary packages
RUN apk add --no-cache bash curl

WORKDIR /app

# Copy package files
COPY package.json ./

# Copy source code
COPY . ./

# Create a simple standalone tsconfig.json for Docker build (CommonJS)
RUN echo '{ \
  "compilerOptions": { \
    "lib": ["ESNext"], \
    "target": "ES2020", \
    "module": "CommonJS", \
    "moduleResolution": "node", \
    "declaration": true, \
    "outDir": "dist", \
    "noEmit": false, \
    "strict": true, \
    "esModuleInterop": true, \
    "skipLibCheck": true, \
    "experimentalDecorators": true, \
    "emitDecoratorMetadata": true, \
    "forceConsistentCasingInFileNames": true, \
    "allowSyntheticDefaultImports": true \
  }, \
  "include": ["src/**/*", "!src/scripts/**/*"], \
  "exclude": ["node_modules", "dist", "src/scripts"] \
}' > tsconfig.json

# Modify package.json to remove workspace dependency
RUN sed -i 's/"shared": "workspace:\*"/"shared": ".\/shared\/dist"/g' package.json

# Install all dependencies (including dev for build)
RUN npm install

# Build the application
RUN npm run build

# Generate Prisma client
RUN npx prisma generate

# Add start script to package.json using Node.js
RUN node -e "const pkg = JSON.parse(require('fs').readFileSync('package.json', 'utf8')); pkg.scripts.start = 'node dist/index.js'; require('fs').writeFileSync('package.json', JSON.stringify(pkg, null, 2));"

# Create a startup script
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'set -e' >> /app/start.sh && \
    echo 'echo "Starting application..."' >> /app/start.sh && \
    echo 'echo "Environment check:"' >> /app/start.sh && \
    echo 'echo "DATABASE_URL: ${DATABASE_URL:0:20}..."' >> /app/start.sh && \
    echo 'echo "BETTER_AUTH_SECRET: ${BETTER_AUTH_SECRET:0:10}..."' >> /app/start.sh && \
    echo 'echo "BETTER_AUTH_URL: $BETTER_AUTH_URL"' >> /app/start.sh && \
    echo 'echo "PORT: $PORT"' >> /app/start.sh && \
    echo 'echo "Running Prisma migration..."' >> /app/start.sh && \
    echo 'npx prisma migrate deploy || echo "Migration failed, continuing..."' >> /app/start.sh && \
    echo 'echo "Starting server..."' >> /app/start.sh && \
    echo 'node dist/index.js' >> /app/start.sh && \
    chmod +x /app/start.sh

EXPOSE 3000

CMD ["/app/start.sh"]
