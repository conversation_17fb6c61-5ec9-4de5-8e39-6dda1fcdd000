{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch"}, "dependencies": {"@prisma/client": "^6.12.0", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "better-auth": "^1.3.3", "hono": "^4.7.11", "prisma": "^6.12.0", "shared": "workspace:*"}, "devDependencies": {"@types/bun": "latest"}}