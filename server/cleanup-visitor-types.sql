-- Cleanup script to remove tipePengunjung and organizationId for admin/superadmin users
-- Only users with role 'user' should have tipePengunjung

-- Update admin and superadmin users to remove tipePengunjung and organizationId
UPDATE "user" 
SET 
  "tipePengunjung" = NULL,
  "organizationId" = NULL,
  "nomorTelepon" = NULL
WHERE role IN ('admin', 'superadmin');

-- Verify the changes
SELECT 
  id, 
  name, 
  email, 
  role, 
  "tipePengunjung", 
  "organizationId", 
  "nomorTelepon"
FROM "user" 
ORDER BY role, name;
