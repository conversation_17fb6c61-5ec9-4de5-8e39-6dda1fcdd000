# Safety Induction Management System 🛡️

A comprehensive safety induction management system built with modern web technologies for managing visitor safety training, quiz assessments, and compliance tracking for industrial facilities.

## 🌐 Live Deployment

- **Frontend**: [https://safety-induction.netlify.app](https://safety-induction.netlify.app/login)
- **Backend API**: [https://safety-api.rl5j77.easypanel.host](https://safety-api.rl5j77.easypanel.host)

## 📋 Overview

The Safety Induction Management System is a full-stack TypeScript application designed to manage safety training, interactive quiz assessments, visitor management, and compliance tracking for industrial facilities. The system supports multiple user roles (USER, ADMIN, SUPERADMIN) with role-based access control and provides comprehensive dashboards for monitoring safety induction activities.

## ✨ Key Features

### 🔐 Authentication & Authorization
- **Better Auth Integration**: Secure email/password authentication with JWT tokens
- **Multi-Role System**: USER, ADMIN, and SUPERADMIN roles with hierarchical permissions
- **Session Management**: Secure cross-origin session handling with 7-day expiration
- **Protected Routes**: Route-level access control with role-based restrictions
- **Separate Login Portals**: Dedicated login interfaces for users and administrators

### 🎓 Interactive Safety Training & Quiz System
- **Video-Based Training**: Safety induction video content with completion tracking
- **Interactive Quiz Assessment**: Multi-choice questions with real-time scoring
- **Passing Score Requirement**: 80% minimum score to pass safety assessment
- **Quiz Retake Functionality**: Allow users to retake failed assessments
- **Progress Tracking**: Monitor user completion and performance metrics
- **Instant Results**: Immediate feedback with pass/fail status

### 👥 User Management
- **Three-Tier Role System**: USER (quiz access), ADMIN (limited management), SUPERADMIN (full access)
- **User Registration**: Self-service account creation with email verification
- **Profile Management**: User profile and settings management
- **Admin User Management**: CRUD operations for user accounts (ADMIN/SUPERADMIN only)
- **Role Assignment**: Dynamic role management and permission control

### 📊 Dashboard & Analytics
- **Role-Specific Dashboards**: Customized interfaces for USER, ADMIN, and SUPERADMIN
- **Real-time Statistics**: Visitor registration, approval metrics, and quiz completion rates
- **Interactive Charts**: Data visualization with Recharts library
- **Multi-facility Support**: Separate tracking for PAM, SMA, and IBM facility types
- **Responsive Design**: Mobile-first approach with TailwindCSS

### 🏢 Visitor Management
- **Visitor Registration**: Comprehensive visitor data collection and validation
- **Approval Workflow**: Multi-stage approval process with status tracking
- **Status Monitoring**: Real-time visitor status updates (approved/pending/rejected)
- **Company Integration**: Multi-company visitor management system
- **Location Tracking**: GPS coordinates for visitor check-ins

### 🛠️ System Administration
- **Content Management**: Training material and quiz content administration
- **Backup & Restore**: Data backup and recovery tools
- **System Logs**: Comprehensive audit logging and monitoring
- **Role Management**: Advanced permission management with granular controls
- **Digital Permit System**: Permit management and tracking capabilities

### 🏗️ Technical Stack
- **Frontend**: React 19 + TypeScript + Vite + TailwindCSS
- **Backend**: Hono + TypeScript + Better Auth + Prisma
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Better Auth with JWT tokens and role-based access
- **UI Components**: Radix UI + shadcn/ui + Lucide Icons
- **Charts & Visualization**: Recharts for dashboard analytics
- **Deployment**: Netlify (Frontend) + EasyPanel (Backend)
- **Package Management**: Bun workspaces with shared TypeScript types
- **Development Tools**: ESLint, TypeScript, Concurrently for multi-service development

## 📁 Project Structure

```
safety-induction/
├── client/                           # React Frontend Application
│   ├── src/
│   │   ├── components/              # Reusable UI components
│   │   │   ├── auth/               # Authentication components (ProtectedRoute)
│   │   │   ├── dashboard/          # Dashboard-specific components
│   │   │   ├── layout/             # Layout components (Header, Sidebar, AdminSidebar)
│   │   │   └── ui/                 # Base UI components (shadcn/ui, PAMLogo)
│   │   ├── contexts/               # React contexts (AuthContext)
│   │   ├── lib/                    # Utility libraries and configurations
│   │   ├── pages/                  # Page components
│   │   │   ├── auth/              # Authentication pages (Login, Signup, userAuth)
│   │   │   ├── admin/             # Admin dashboard pages (limited access)
│   │   │   ├── superadmin/        # Superadmin dashboard pages (full access)
│   │   │   └── user/              # User pages (VisitorSafetyQuiz)
│   │   └── types/                  # Frontend-specific types
│   ├── public/                     # Static assets
│   ├── netlify.toml               # Netlify deployment configuration
│   └── package.json               # Frontend dependencies
├── server/                          # Hono Backend API
│   ├── src/
│   │   ├── lib/                   # Core libraries (auth configuration, database)
│   │   ├── middleware/            # Authentication and role-based middleware
│   │   ├── scripts/               # Utility scripts
│   │   │   ├── create-superadmin.ts  # Create superadmin user script
│   │   │   └── setup-production.ts  # Production setup script
│   │   ├── types/                 # Backend-specific types (shared types)
│   │   └── index.ts               # Main server entry point with API routes
│   ├── prisma/                    # Database schema and migrations
│   │   ├── schema.prisma          # Database schema (User, Session, Account, Verification)
│   │   └── migrations/            # Database migration files
│   ├── Dockerfile                 # Docker configuration for deployment
│   ├── server.js                  # Alternative server entry point
│   └── package.json               # Backend dependencies
├── shared/                          # Shared TypeScript definitions
│   └── src/types/                 # Common types used by both client and server
└── package.json                    # Root package.json with workspaces
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** 18+ or **Bun** runtime
- **PostgreSQL** database
- **Git** for version control

### 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd safety-induction
   ```

2. **Install dependencies**
   ```bash
   # Using Bun (recommended)
   bun install
   
   # Or using npm
   npm install
   ```

3. **Environment Setup**
   
   **Server Environment** (`server/.env`):
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/safety_induction"
   BETTER_AUTH_SECRET="your-secret-key-here"
   BETTER_AUTH_URL="https://safety-api.rl5j77.easypanel.host"
   CORS_ORIGIN="https://safety-induction.netlify.app"
   NODE_ENV="production"
   PORT=3000
   ```

   **Client Environment** (`client/.env`):
   ```env
   VITE_API_URL=https://safety-api.rl5j77.easypanel.host
   VITE_APP_NAME=Safety Induction System
   VITE_APP_VERSION=1.0.0
   ```

4. **Database Setup**
   ```bash
   cd server
   npx prisma migrate deploy
   npx prisma generate
   ```

### 🔧 Creating Admin Users

The system includes scripts to create admin and superadmin users for system administration:

1. **Using the create-superadmin script**:
   ```bash
   cd server
   npx tsx src/scripts/create-superadmin.ts
   ```

2. **Using the production setup script** (checks for existing superadmin):
   ```bash
   cd server
   npx tsx src/scripts/setup-production.ts
   ```

3. **Manual admin/superadmin creation** (if needed):
   ```bash
   # First create a regular user through the signup form
   # Then update the user role in the database:
   cd server
   npx prisma studio
   # Navigate to User table and change role from "USER" to "ADMIN" or "SUPERADMIN"
   ```

### 🚪 Access Points

The system provides different access points for different user types:

- **User Portal**: `/user/login` - For regular users to access safety quiz
- **Admin Portal**: `/login` - For ADMIN and SUPERADMIN users to access management features
- **Quiz Interface**: `/user/quiz` - Interactive safety training and assessment

### 🏃‍♂️ Development

```bash
# Run all services in development mode
bun run dev

# Or run individual services
bun run dev:shared  # Watch and compile shared types
bun run dev:server  # Run the Hono backend
bun run dev:client  # Run the Vite dev server for React
```

### 🏗️ Building for Production

```bash
# Build everything
bun run build

# Or build individual parts
bun run build:shared  # Build the shared types package
bun run build:server  # Build the backend
bun run build:client  # Build the React frontend
```

## 🌐 Deployment

### Current Deployment

- **Frontend**: Deployed on [Netlify](https://safety-induction.netlify.app/login)
- **Backend**: Deployed on [EasyPanel](https://safety-api.rl5j77.easypanel.host)
- **Database**: PostgreSQL hosted on cloud provider

### Deployment Configuration

**Netlify Configuration** (`client/netlify.toml`):
- Automatic builds from repository
- API proxy to backend server
- SPA routing configuration
- Security headers

**EasyPanel Configuration**:
- Docker-based deployment
- Environment variables management
- Automatic SSL certificates
- Health checks and monitoring

## 👥 User Roles & Access

### USER Role
- **Quiz Access**: Complete safety induction video and quiz assessment
- **Profile Management**: View and update personal profile information
- **Training Progress**: Track quiz completion and scores
- **Limited Access**: Cannot access administrative functions
- **Dedicated Portal**: Access through `/user/login` endpoint

### ADMIN Role
- **Limited Management**: Access to basic administrative functions
- **Visitor Management**: Manage visitor registrations and approvals
- **User Oversight**: View user information and quiz completion status
- **Content Management**: Basic content administration capabilities
- **Dashboard Access**: Admin-specific dashboard with limited analytics
- **Restricted Permissions**: Cannot manage other admin users or system settings

### SUPERADMIN Role
- **Full System Access**: Complete administrative control over the system
- **User Management**: CRUD operations for all user accounts and role assignments
- **Visitor Management**: Full visitor management and approval workflows
- **Content Management**: Complete training material and quiz content administration
- **System Configuration**: Access to system settings and configuration
- **Backup & Restore**: Data backup and recovery operations
- **System Monitoring**: Comprehensive audit logging and system monitoring
- **Role Management**: Advanced permission management and role creation
- **Digital Permits**: Full permit management and tracking capabilities

## 📊 Application Features

### Safety Training & Quiz System
- **Video-Based Learning**: Embedded safety induction videos with completion tracking
- **Interactive Assessments**: Multi-choice quiz questions with immediate feedback
- **Scoring System**: Real-time scoring with 80% passing requirement
- **Retake Functionality**: Users can retake failed assessments
- **Progress Tracking**: Monitor completion rates and performance analytics
- **Mobile Responsive**: Optimized for mobile devices and tablets

### Dashboard Analytics
- **Role-Based Dashboards**: Customized interfaces for USER, ADMIN, and SUPERADMIN
- **Real-time Statistics**: Live visitor counts, approval metrics, and quiz completion rates
- **Multi-facility Support**: Separate tracking for PAM, SMA, and IBM facilities
- **Interactive Charts**: Data visualization using Recharts library with weekly/monthly views
- **Performance Metrics**: User engagement and training effectiveness analytics
- **Responsive Design**: Mobile-first approach with TailwindCSS

### Visitor Management System
- **Registration Process**: Comprehensive visitor data collection with validation
- **Approval Workflow**: Multi-stage approval with status tracking and notifications
- **Company Integration**: Support for multiple company types and affiliations
- **Status Monitoring**: Real-time visitor status updates (approved/pending/rejected/expired)
- **Location Services**: GPS coordinate tracking for visitor check-ins
- **Digital Permits**: Integrated permit management and tracking system

### Content Management
- **Training Materials**: Upload and manage safety training videos and documents
- **Quiz Administration**: Create, edit, and manage quiz questions and answers
- **Content Versioning**: Track content changes and updates with audit trails
- **Multi-media Support**: Support for various file formats and media types
- **Content Scheduling**: Schedule content updates and training sessions

## 🔧 API Documentation

### Authentication Endpoints
```
POST /api/auth/sign-up/email    # User registration with role assignment
POST /api/auth/sign-in/email    # User login with session creation
POST /api/auth/sign-out         # User logout and session cleanup
GET  /api/auth/get-session      # Get current session and user info
```

### Dashboard Endpoints
```
GET  /api/dashboard/stats       # Get dashboard statistics (role-based)
GET  /api/dashboard/chart-data  # Get chart data for analytics
GET  /api/dashboard/chart/weekly # Get weekly chart data
GET  /api/dashboard/visitors    # Get visitor data and status
```

### Protected User Endpoints
```
GET  /api/protected/profile     # Get user profile information
```

### Admin Endpoints (ADMIN/SUPERADMIN only)
```
GET    /api/protected/admin/users    # Get all users (SUPERADMIN only)
POST   /api/admin/users              # Create new user
PUT    /api/admin/users/:id          # Update user information
DELETE /api/admin/users/:id          # Delete user account
```

### Quiz & Training Endpoints
```
GET  /api/quiz/questions        # Get quiz questions (USER role)
POST /api/quiz/submit           # Submit quiz answers
GET  /api/quiz/results/:userId  # Get quiz results and scores
POST /api/training/complete     # Mark training video as completed
```

## 🗄️ Database Schema

### Core Tables
- **User**: User accounts with role-based access (USER, ADMIN, SUPERADMIN)
- **Session**: User session management with expiration tracking
- **Account**: Authentication account linking for Better Auth
- **Verification**: Email verification tokens and security

### Enhanced User Schema
```sql
User {
  id: String (Primary Key, CUID)
  name: String
  email: String (Unique)
  emailVerified: Boolean (Default: false)
  image: String? (Optional profile image)
  role: Enum (USER, ADMIN, SUPERADMIN) (Default: USER)
  banned: Boolean (Default: false)
  banReason: String? (Optional ban reason)
  banExpires: DateTime? (Optional ban expiration)
  createdAt: DateTime (Auto-generated)
  updatedAt: DateTime (Auto-updated)
  sessions: Session[] (One-to-many relationship)
  accounts: Account[] (One-to-many relationship)
}
```

### Session Management Schema
```sql
Session {
  id: String (Primary Key, CUID)
  expiresAt: DateTime
  token: String (Unique session token)
  ipAddress: String? (Optional IP tracking)
  userAgent: String? (Optional browser tracking)
  userId: String (Foreign key to User)
  impersonatedBy: String? (Optional admin impersonation)
  createdAt: DateTime
  updatedAt: DateTime
}
```

## 🚨 Important Notes

### Current Implementation Status
- ✅ **User Authentication**: Fully implemented with Better Auth
- ✅ **Role-Based Access Control**: Complete with USER, ADMIN, SUPERADMIN roles
- ✅ **Interactive Quiz System**: Fully functional with scoring and retake capability
- ✅ **Dashboard Analytics**: Real-time statistics and charts implemented
- ⚠️ **Some admin features**: Still using placeholder/dummy data
- 📝 **Content management**: Basic implementation with room for enhancement
- 🧪 **Testing environment**: May contain sample data for demonstration

### Security Considerations
- 🔐 **Better Auth Integration**: Secure JWT-based authentication with 7-day sessions
- 🍪 **Cross-origin cookies**: Properly configured for production deployment
- 🛡️ **Role-based access control**: Hierarchical permissions (SUPERADMIN > ADMIN > USER)
- 🔒 **Environment variables**: All sensitive data properly externalized
- 🚫 **Route protection**: All sensitive routes protected with authentication middleware
- 🔑 **Session management**: Automatic session cleanup and expiration handling

### Performance & Optimization
- ⚡ **Vite**: Lightning-fast development builds and hot module replacement
- 📦 **Bun**: Efficient package management and faster dependency resolution
- 🎯 **Code splitting**: Optimized loading with route-based code splitting
- 📱 **Responsive design**: Mobile-first approach optimized for all devices
- 🚀 **Production builds**: Optimized bundles with tree shaking and minification
- 💾 **Database optimization**: Efficient queries with Prisma ORM and connection pooling

## 🎯 Usage Guide

### For Regular Users (USER Role)
1. **Registration**: Sign up at `/user/register` with email and password
2. **Login**: Access the system through `/user/login`
3. **Training**: Watch the safety induction video completely
4. **Assessment**: Complete the interactive quiz with minimum 80% score
5. **Retake**: Retake the quiz if needed to achieve passing score

### For Administrators (ADMIN Role)
1. **Login**: Access through `/login` with admin credentials
2. **Dashboard**: View admin-specific analytics and metrics
3. **Visitor Management**: Manage visitor registrations and approvals
4. **User Oversight**: Monitor user quiz completion and performance
5. **Content Management**: Basic content administration capabilities

### For Super Administrators (SUPERADMIN Role)
1. **Full Access**: Complete system administration capabilities
2. **User Management**: Create, update, and delete user accounts
3. **Role Assignment**: Assign and modify user roles
4. **System Configuration**: Access to all system settings
5. **Advanced Analytics**: Comprehensive reporting and monitoring

## 📚 Learn More

### Technology Documentation
- [Better Auth Documentation](https://www.better-auth.com/docs) - Authentication and session management
- [Hono Documentation](https://hono.dev/docs) - Fast web framework for the backend
- [Prisma Documentation](https://www.prisma.io/docs) - Database ORM and migrations
- [React Documentation](https://react.dev/learn) - Frontend framework and hooks
- [TailwindCSS Documentation](https://tailwindcss.com/docs) - Utility-first CSS framework
- [Vite Documentation](https://vitejs.dev/guide/) - Build tool and development server
- [Bun Documentation](https://bun.sh/docs) - JavaScript runtime and package manager
- [Recharts Documentation](https://recharts.org/en-US/) - Chart library for React

### Deployment Platforms
- [Netlify Documentation](https://docs.netlify.com/) - Frontend deployment and hosting
- [EasyPanel Documentation](https://easypanel.io/docs) - Backend deployment and container management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
