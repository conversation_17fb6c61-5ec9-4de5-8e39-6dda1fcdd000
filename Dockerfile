# Stage 1: Build dependencies and application
FROM oven/bun:1.2.19 AS builder
WORKDIR /app

# Install OpenSSL for runtime compatibility
RUN apt-get update -y && apt-get install -y openssl

# Check OpenSSL version for debugging
RUN openssl version

# Check OpenSSL version for debugging
RUN openssl version

# Copy package files first for better caching
COPY package.json bun.lock ./
COPY client/package.json ./client/
COPY server/package.json ./server/
COPY shared/package.json ./shared/

# Copy source code (including pre-generated Prisma client)
COPY . .

# Install dependencies (skip postinstall scripts to avoid regenerating Prisma client)
RUN bun install --ignore-scripts

# Build for single origin
RUN bun run build:single

# Stage 2: Create runtime image
FROM oven/bun:1.2.19
WORKDIR /app

# Install OpenSSL for runtime
RUN apt-get update -y && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy built application from builder stage
COPY --from=builder /app/server/dist ./server/dist
COPY --from=builder /app/server/static ./server/static
COPY --from=builder /app/server/package.json ./server/
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/server/node_modules ./server/node_modules
COPY --from=builder /app/package.json ./

EXPOSE 3000
CMD ["bun", "run", "start:single"]